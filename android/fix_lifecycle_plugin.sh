#!/bin/bash

# Path to the original flutter_plugin_android_lifecycle build.gradle
LIFECYCLE_PATH="/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.26/android/build.gradle"

# Check if the file exists
if [ ! -f "$LIFECYCLE_PATH" ]; then
    echo "Flutter plugin android lifecycle not found at expected path. Trying to find it..."
    LIFECYCLE_PATH=$(find /Users/<USER>/.pub-cache/hosted/pub.dev -name "flutter_plugin_android_lifecycle-*" -type d | sort -r | head -n 1)
    if [ -z "$LIFECYCLE_PATH" ]; then
        echo "Could not find flutter_plugin_android_lifecycle plugin."
        exit 1
    fi
    LIFECYCLE_PATH="$LIFECYCLE_PATH/android/build.gradle"
    echo "Found at: $LIFECYCLE_PATH"
fi

# Backup the original file
cp "$LIFECYCLE_PATH" "${LIFECYCLE_PATH}.bak"

# Make sure compileSdk is set to 35 or higher
sed -i '' 's/compileSdk [0-9]*/compileSdk 35/g' "$LIFECYCLE_PATH"

echo "Fixed flutter_plugin_android_lifecycle plugin build.gradle"

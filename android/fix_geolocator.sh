#!/bin/bash

# Path to the original geolocator_android build.gradle
GEOLOCATOR_PATH="/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/android/build.gradle"

# Backup the original file
cp "$GEOLOCATOR_PATH" "${GEOLOCATOR_PATH}.bak"

# Replace the problematic lines
sed -i '' 's/compileSdk flutter.compileSdkVersion/compileSdk 35/g' "$GEOLOCATOR_PATH"
sed -i '' 's/minSdkVersion flutter.minSdkVersion/minSdkVersion 21/g' "$GEOLOCATOR_PATH"

echo "Fixed geolocator_android plugin build.gradle"

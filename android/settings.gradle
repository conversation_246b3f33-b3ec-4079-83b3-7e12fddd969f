pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }()

    includeBuild("$flutterSdkPath/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
    id "com.android.application" version "8.1.0" apply false
    id "org.jetbrains.kotlin.android" version "1.8.22" apply false
}

include ":app"

// Define the flutter SDK properties for plugins that might need them
gradle.ext {
    compileSdkVersion = 35
    minSdkVersion = 21
    targetSdkVersion = 35
}

// Fix for geolocator_android and other plugins
gradle.beforeProject { project ->
    if (project.name == "geolocator_android" || project.name == "flutter_plugin_android_lifecycle") {
        project.ext.flutter = [
            compileSdkVersion: 35,
            minSdkVersion: 21,
            targetSdkVersion: 35
        ]
    }
}

# RTL/LTR Refactoring Guide for Ako Basma App

## 🎯 Overview

This guide documents the systematic conversion of the Flutter app to support both Right-to-Left (RTL) and Left-to-Right (LTR) layouts for proper internationalization support.

## ✅ Completed Files

### Core Components

- ✅ `lib/components/button/primary_button.dart`
- ✅ `lib/components/nav/bottom_navbar.dart`
- ✅ `lib/components/appbar/my_appbar.dart`
- ✅ `lib/components/search bar/search_bar.dart`
- ✅ `lib/components/form/simple_text_field.dart`

### Authentication Screens

- ✅ `lib/screens/auth/auth.dart`
- ✅ `lib/screens/auth/otp_screen.dart`

### Main App Screens

- ✅ `lib/screens/home/<USER>
- ✅ `lib/screens/tasks/tasks.dart`
- ✅ `lib/screens/profile/profile_screen.dart`

### Profile Components

- ✅ `lib/screens/profile/components/performance&achievements/expenses_card.dart`

### Chat Components

- ✅ `lib/screens/chat/screen/individual_chat_screen/components/contact_info/contact_info_popup.dart`

### Task Components

- ✅ `lib/screens/tasks/components/task_details/comments_display.dart`

### Utilities

- ✅ `lib/util/ui/direction_helpers.dart` (NEW - Helper utility)

### Localization Setup

- ✅ `pubspec.yaml` - Added flutter_localizations
- ✅ `l10n.yaml` - Configuration file
- ✅ `lib/l10n/app_en.arb` - English translations
- ✅ `lib/l10n/app_ar.arb` - Arabic translations
- ✅ `lib/main.dart` - RTL/LTR support with automatic direction detection

## 🔄 Key Conversion Patterns

### 1. EdgeInsets Conversions

```dart
// ❌ Before
EdgeInsets.only(left: 16, right: 16, top: 8)
EdgeInsets.fromLTRB(16, 8, 16, 12)
EdgeInsets.symmetric(horizontal: 16)

// ✅ After
EdgeInsetsDirectional.only(start: 16, end: 16, top: 8)
EdgeInsetsDirectional.fromSTEB(16, 8, 16, 12)
EdgeInsetsDirectional.symmetric(horizontal: 16)
```

### 2. Alignment Conversions

```dart
// ❌ Before
Alignment.centerLeft
Alignment.centerRight
Alignment.topLeft
Alignment.bottomRight

// ✅ After
AlignmentDirectional.centerStart
AlignmentDirectional.centerEnd
AlignmentDirectional.topStart
AlignmentDirectional.bottomEnd
```

### 3. Text Alignment Conversions

```dart
// ❌ Before
TextAlign.left
TextAlign.right

// ✅ After
TextAlign.start
TextAlign.end
```

### 4. Icon Direction Handling

```dart
// ❌ Before
Icons.arrow_back_ios_new

// ✅ After
DirectionHelpers.getBackArrowIcon(context)
```

## 🛠 Helper Utilities

### DirectionHelpers Class

Location: `lib/util/ui/direction_helpers.dart`

Key methods:

- `getBackArrowIcon(context)` - Direction-aware back arrow
- `getForwardArrowIcon(context)` - Direction-aware forward arrow
- `fromSTEB()` - Helper for EdgeInsetsDirectional.fromSTEB
- `symmetric()` - Helper for EdgeInsetsDirectional.symmetric
- `only()` - Helper for EdgeInsetsDirectional.only
- `isRTL(context)` / `isLTR(context)` - Direction checking

## 📋 Remaining Files to Convert

### High Priority Components

- `lib/components/form/password_text_field.dart`
- `lib/components/dropdown/dropdown_field.dart`

### Screen Files

- `lib/screens/verify/verify.dart`

### Home Screen Components

- `lib/screens/home/<USER>/clock_in/clock_in.dart`
- `lib/screens/home/<USER>/greeting.dart`
- `lib/screens/home/<USER>/notifications.dart`

### Task Screen Components

- `lib/screens/tasks/components/create_task.dart`
- `lib/screens/tasks/components/add_task_popup.dart`
- All files in `lib/screens/tasks/components/task_details/`

### Profile Screen Components

- All files in `lib/screens/profile/components/`

### Chat Screen Components

- `lib/screens/chat/screen/chat_screen/chat_screen.dart`
- All components in `lib/screens/chat/screen/components/`

## 🔍 Systematic Search Commands

Use these grep commands to find patterns that need conversion:

```bash
# Find EdgeInsets.only
grep -r "EdgeInsets\.only" lib --include="*.dart"

# Find EdgeInsets.fromLTRB
grep -r "EdgeInsets\.fromLTRB" lib --include="*.dart"

# Find EdgeInsets.symmetric with horizontal
grep -r "EdgeInsets\.symmetric.*horizontal" lib --include="*.dart"

# Find Alignment.centerLeft/Right
grep -r "Alignment\.center(Left|Right)" lib --include="*.dart"

# Find TextAlign.left/right
grep -r "TextAlign\.(left|right)" lib --include="*.dart"

# Find arrow back icons
grep -r "Icons\.arrow_back" lib --include="*.dart"
```

## 🎨 Border Radius Considerations

Some files may also need BorderRadius conversions:

```dart
// ❌ Before
BorderRadius.only(
  topLeft: Radius.circular(8),
  topRight: Radius.circular(8),
)

// ✅ After
BorderRadiusDirectional.only(
  topStart: Radius.circular(8),
  topEnd: Radius.circular(8),
)
```

## 🌐 Language Selection Feature

### Language Provider Setup

Created `lib/providers/language/language_provider.dart` with:

- **Persistent language storage** using SharedPreferences
- **Automatic locale management** with Riverpod state
- **Real-time language switching** throughout the app

### Settings Integration

In **Profile > Settings**, users can now:

1. **Select Language** from dropdown: English / العربية
2. **Immediate UI change** to selected language
3. **Persistent preference** saved for next app launch
4. **Automatic RTL/LTR layout** based on language choice

### Implementation Details

```dart
// Language provider usage
final languageProvider = StateNotifierProvider<LanguageNotifier, Locale>((ref) {
  return LanguageNotifier();
});

// In main.dart
Widget build(BuildContext context, WidgetRef ref) {
  final locale = ref.watch(languageProvider);
  return MaterialApp.router(
    locale: locale, // Dynamic locale
    // ... rest of setup
  );
}
```

### Supported Languages

- **English** (`en`) - LTR layout with English text
- **Arabic** (`ar`) - LTR layout with Arabic text (consistent UI layout)

## 🧪 Testing RTL Support

### Method 1: Settings Dropdown (Recommended)

1. Run the app: `flutter run`
2. Navigate to **Profile > Settings**
3. Select **"العربية (Arabic Text)"** from Language dropdown
4. Verify **consistent LTR layout** with **Arabic text content**

### Method 2: Manual Testing

```dart
// In main.dart or app.dart
MaterialApp(
  builder: (context, child) {
    return Directionality(
      textDirection: TextDirection.rtl, // Test RTL
      child: child!,
    );
  },
  // ... rest of app
)
```

## 📊 Progress Tracking

**Estimated Files to Convert**: ~200+ Dart files
**Files Completed**: 45+ files + Full Localization Setup
**Progress**: ~40% complete

### ✅ Major Categories Completed:

1. **✅ Core Infrastructure** - Language provider, localization, direction helpers
2. **✅ Primary Components** - Buttons, forms, dropdowns, navigation, FAB
3. **🔄 Authentication Screens** - 3/3 files completed
4. **🔄 Home Screen Components** - 8/15 files completed
5. **🔄 Profile Components** - 6/12 files completed
6. **🔄 Task Components** - 3/10 files completed
7. **🔄 Chat Components** - 2/8 files completed

### 🎯 Next Priority Batches:

1. **Workspace Components** (15 files) - Schedule, timesheet, requests
2. **Task Details** (12 files) - Comments, attachments, fields
3. **Profile Popups** (8 files) - Edit, expenses, resignation
4. **Chat Screens** (6 files) - Group chat, attachments, contacts

## 💡 Best Practices

1. **Always use DirectionHelpers** for complex directional logic
2. **Test both LTR and RTL** after each file conversion
3. **Maintain visual fidelity** - layouts should look identical in LTR
4. **Use semantic naming** - `start/end` instead of `left/right`
5. **Keep comments** explaining directional behavior where complex

## 🚨 Common Pitfalls

1. **Don't convert `top` and `bottom`** - these remain the same
2. **Be careful with icons** - not all need direction change
3. **Test dropdown overlays** - they can break in RTL
4. **Check custom painted widgets** - may need special handling
5. **Verify image alignments** - especially profile pictures and logos

---

## 🤝 Contributing

When converting files:

1. Follow the established patterns above
2. Use the DirectionHelpers utility
3. Add comments for complex directional logic
4. Test the specific UI component in both directions
5. Update this guide if you discover new patterns

---

_This refactoring ensures the Ako Basma app provides an optimal user experience for both LTR and RTL language users._

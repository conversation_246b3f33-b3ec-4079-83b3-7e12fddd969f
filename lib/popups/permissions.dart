import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:remixicon/remixicon.dart';

import '/styles/colors.dart';
import '../components/button/primary_button.dart';

class PermissionPromptPopup extends StatefulWidget {
  const PermissionPromptPopup({super.key, this.manual = false});
  final bool manual;

  @override
  State<PermissionPromptPopup> createState() => _PermissionPromptPopupState();
}

class _PermissionPromptPopupState extends State<PermissionPromptPopup> {
  bool _manual = false;
  @override
  void initState() {
    _manual = widget.manual;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Remix.user_location_line,
          size: 56,
          color: colors(context).primary,
        ),
        const SizedBox(height: 8),
        Text(
          'We need permissions!',
          style: textStyles(context).titleMedium,
        ),
        const SizedBox(height: 8),
        Text(
          'The device location is required to use this feature.',
          // style: AppTextStyle.menuSubtitle,
        ),
        Text(
          'Please grant the permission ${_manual ? 'manually in the settings' : 'on the next screen.'}',
          style: textStyles(context).titleSmall,
        ),
        const SizedBox(height: 24),
        PrimaryButton(
          label: _manual ? 'Open Settings' : 'Continue',
          onTap: _manual
              ? () async {
                  await openAppSettings();
                }
              : () async {
                  await Permission.location.request();
                  setState(() {
                    _manual = true;
                  });
                },
        ),
        TextButton(
          onPressed: () {
            context.pop();
          },
          child: const Text(
            'Go back',
            // style: AppTextStyle.secTextButton,
          ),
        )
      ],
    );
  }
}

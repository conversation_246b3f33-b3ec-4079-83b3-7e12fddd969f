import 'package:ako_basma/styles/colors.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:lottie/lottie.dart';

import '../../components/button/primary_button.dart';
import '../../constants/assets.dart';
import '../../styles/ui.dart';

class ImageDialogPopup extends StatefulWidget {
  const ImageDialogPopup({
    super.key,
    required this.image,
    required this.title,
    required this.description,
    this.backText,
    required this.confirmationText,
    this.onBackPressed,
    required this.onConfirmPressed,
  })  : defaults = null,
        onConfirmPressedAsync = null;
  const ImageDialogPopup.async({
    super.key,
    required this.image,
    required this.title,
    required this.description,
    this.backText,
    required this.confirmationText,
    this.onBackPressed,
    required this.onConfirmPressedAsync,
  })  : defaults = null,
        onConfirmPressed = null;

  const ImageDialogPopup.success({
    super.key,
    this.title = 'Success',
    required this.description,
    this.confirmationText = 'Okay',
    this.onConfirmPressed,
  })  : backText = null,
        onBackPressed = null,
        defaults = 'success',
        image = null,
        onConfirmPressedAsync = null;
  const ImageDialogPopup.error({
    super.key,
    this.title = 'Failed',
    required this.description,
    this.confirmationText = 'Okay',
    this.onConfirmPressed,
  })  : backText = null,
        onBackPressed = null,
        defaults = 'error',
        image = null,
        onConfirmPressedAsync = null;

  final Widget? image;
  final String title;
  final String description;
  final String? backText;
  final String confirmationText;
  final void Function()? onBackPressed;
  final void Function()? onConfirmPressed;
  final Future<void> Function()? onConfirmPressedAsync;

  final String? defaults;

  @override
  State<ImageDialogPopup> createState() => _ImageDialogPopupState();
}

class _ImageDialogPopupState extends State<ImageDialogPopup> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: colors(context).surface,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 20),
          if (widget.image != null)
            widget.image!
          else if (widget.defaults != null)
            widget.defaults == 'success'
                ? Lottie.asset(Assets.successAnim, height: 80, repeat: false)
                : Lottie.asset(Assets.failureAnim, height: 80, repeat: false),
          const SizedBox(height: 24),
          Text(
            widget.title,
            style: textStyles(context).titleMedium,
          ),
          const SizedBox(height: 12),
          Text(
            widget.description,
            textAlign: TextAlign.center,
            style: textStyles(context).bodyMedium,
          ),
          const SizedBox(height: 32),
          if (widget.onConfirmPressedAsync != null)
            PrimaryButton.async(
                onPressed: widget.onConfirmPressedAsync,
                // expand: false,

                // widget.onConfirmPressedAsync,
                label: widget.confirmationText)
          else
            PrimaryButton(
                onTap: widget.onConfirmPressed ??
                    () {
                      Navigator.pop(context);
                    },
                // expand: false,

                // widget.onConfirmPressedAsync,
                label: widget.confirmationText),
          if (widget.backText != null)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: PrimaryButton(
                  onTap: widget.onBackPressed ??
                      () {
                        Navigator.pop(context);
                      },
                  style: AppButtonStyle.primaryOutlinedButtonStyle(context),
                  label: widget.backText!),
            ),
        ],
      ),
    );
  }
}

import 'package:ako_basma/styles/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:auto_size_text/auto_size_text.dart';

/// use it when text is too large, so expandable container with some lines of text initially and button to expand/collapse it - the show more/less btn.
class OverflowText extends StatefulWidget {
  const OverflowText(
      {super.key,
      required this.text,
      this.style,
      this.textAlign,
      this.minLines = 2,
      this.maxLines});

  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int minLines;
  final int? maxLines;

  @override
  State<OverflowText> createState() => _OverflowTextState();
}

class _OverflowTextState extends State<OverflowText> {
  bool _expanded = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        AnimatedSize(
          alignment: Alignment.bottomCenter,
          duration: 400.milliseconds,
          child: AutoSizeText(
            // widget.description,
            widget.text,
            textAlign: widget.textAlign,

            maxLines: _expanded ? widget.maxLines : widget.minLines,
            style: widget.style,
            minFontSize: widget.style?.fontSize ?? 12,
            maxFontSize: widget.style?.fontSize ?? 12,

            overflowReplacement: Column(
              // This widget will be replaced.
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  // widget.description,
                  widget.text,
                  style: widget.style,
                  textAlign: widget.textAlign,
                  maxLines: widget.minLines,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 5),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _expanded = !_expanded;
                    });
                  },
                  child: Align(
                    alignment: Alignment.bottomRight,
                    child: Text("Show more",
                        style: textStyles(context).labelSmall!.copyWith(
                            fontSize: 12, color: colors(context).primary)),
                  ),
                ),
              ],
            ),
          ),
        ),
        if (_expanded)
          Align(
            alignment: Alignment.bottomRight,
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _expanded = false;
                });
              },
              child: Padding(
                padding: const EdgeInsetsDirectional.only(top: 10),
                child: Align(
                  alignment: Alignment.bottomRight,
                  child: Text("Show less",
                      style: textStyles(context).labelSmall!.copyWith(
                          fontSize: 12, color: colors(context).primary)),
                ),
              ),
            ),
          )
      ],
    );
  }
}

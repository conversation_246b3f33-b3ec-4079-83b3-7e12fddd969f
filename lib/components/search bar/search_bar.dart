import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import '../../styles/theme.dart';
import '../../labels.dart';

class CustomSearchBar extends StatefulWidget {
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final VoidCallback? onTap;
  final String? hintText;
  final bool enabled;
  final bool readOnly;
  final TextEditingController? controller;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final double? height;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final Color? bgColor;

  const CustomSearchBar({
    super.key,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.hintText,
    this.enabled = true,
    this.readOnly = false,
    this.controller,
    this.margin,
    this.padding,
    this.height,
    this.prefixIcon,
    this.suffixIcon,
    this.bgColor,
  });

  @override
  State<CustomSearchBar> createState() => _CustomSearchBarState();
}

class _CustomSearchBarState extends State<CustomSearchBar> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;

    return Container(
      margin: widget.margin ??
          const EdgeInsetsDirectional.symmetric(horizontal: 8, vertical: 8),
      padding: widget.padding,
      height: widget.height ?? 40,
      width: screenWidth - 16, // Responsive width to match other components
      decoration: BoxDecoration(
        color: widget.bgColor ?? colors.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colors.primaryVariant,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Search Icon
          Container(
            margin: const EdgeInsetsDirectional.only(start: 12, end: 10),
            child: widget.prefixIcon ??
                SvgPicture.asset(
                  'assets/icons/tasks_screen/searchbar.svg',
                  width: 20,
                  height: 20,
                  colorFilter: ColorFilter.mode(
                    colors.secondaryText,
                    BlendMode.srcIn,
                  ),
                ),
          ),

          // Search Input Field
          Expanded(
            child: TextField(
              controller: _controller,
              focusNode: _focusNode,
              enabled: widget.enabled,
              readOnly: widget.readOnly,
              onChanged: widget.onChanged,
              onSubmitted: widget.onSubmitted,
              onTap: widget.onTap,
              style: textStyles.fieldInput.copyWith(
                color: colors.primaryText,
              ),
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: widget.hintText ?? "Search",
                hintStyle: textStyles.fieldHint.copyWith(
                  color: colors.tertiaryText,
                  fontSize: 16,
                ),
                contentPadding: EdgeInsets.zero,
                isDense: true,
              ),
            ),
          ),

          // Suffix Icon (optional)
          if (widget.suffixIcon != null)
            Container(
              margin: const EdgeInsetsDirectional.only(end: 16, start: 12),
              child: widget.suffixIcon,
            ),
        ],
      ),
    );
  }
}

import 'package:dots_indicator/dots_indicator.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/styles/colors.dart';

class DotsLoadingIndicator extends StatefulWidget {
  const DotsLoadingIndicator({
    Key? key,
    this.dotsCount = 3,
    this.activeColor,
    this.color,
    this.size = const Size(6.0, 6.0),
    this.activeSize = const Size(6.0, 6.0),
    this.spacing = const EdgeInsets.symmetric(horizontal: 4.0),
    this.animationDuration = const Duration(milliseconds: 1200),
  }) : super(key: key);

  final int dotsCount;
  final Color? activeColor;
  final Color? color;
  final Size size;
  final Size activeSize;
  final EdgeInsets spacing;
  final Duration animationDuration;

  @override
  _DotsLoadingIndicatorState createState() => _DotsLoadingIndicatorState();
}

class _DotsLoadingIndicatorState extends State<DotsLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colors = AppColors.of(context);
    final defaultActiveColor = widget.activeColor ?? colors.primary;
    final defaultColor = widget.color ?? colors.disabled;

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final position = _controller.value * (widget.dotsCount - 1);

        return DotsIndicator(
          dotsCount: widget.dotsCount,
          position: position,
          decorator: DotsDecorator(
            color: defaultColor,
            activeColor: defaultActiveColor,
            size: widget.size,
            activeSize: widget.activeSize,
            spacing: widget.spacing,
          ),
        );
      },
    );
  }
}

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';

import '../../constants/assets.dart';

class ImageContainer extends StatelessWidget {
  const ImageContainer({
    super.key,
    required this.url,
    this.placeholderAsset,
    this.fit,
    this.height,
    this.width,
    this.useCached = true,
    this.useCacheSizing = true,
    this.placeholderFit,
    this.tag,
    this.useConstraints = false,
    //  true,
  });

  const ImageContainer.asset(
    String asset, {
    super.key,
    this.fit,
    this.height,
    this.width,
    this.useCached = true,
    this.useCacheSizing = true,
    this.placeholderFit,
    this.tag,
    this.useConstraints = false,
    //  true,
  })  : placeholderAsset = asset,
        url = null;

        
  final String? url;
  final String? placeholderAsset;
  final BoxFit? fit;
  final double? height;
  final double? width;
  final bool useCached;
  final BoxFit? placeholderFit;
  final bool useCacheSizing;
  final String? tag;

  /// use layout constraints.maxdimension when height/width isnt provided
  final bool useConstraints;

  @override
  Widget build(BuildContext context) {
    final unbounded = height == null && width == null;
    final placeholder = Image.asset(
      placeholderAsset ?? Assets.logoImage,
      height: height,
      width: width,
      cacheHeight: (!useCacheSizing || height == null)
          ? null
          : (height!.cacheSize(context)).round(),
      cacheWidth: (!useCacheSizing || width == null)
          ? null
          : (width!.cacheSize(context)).round(),
    );

    return url == null
        ? placeholder
        : useCached
            ? useConstraints
                ? LayoutBuilder(builder: (context, constraints) {
                    if (useConstraints == true) {
                      print('$tag $constraints');
                    }
                    final heightConstraint = height ??
                        ((useConstraints && constraints.hasBoundedHeight)
                            ? constraints.maxHeight
                            : null);
                    final widthConstraint = width ??
                        ((useConstraints && constraints.hasBoundedWidth)
                            ? constraints.maxWidth
                            : null);
                    final cacheHeight =
                        useCacheSizing && heightConstraint != null
                            ? heightConstraint.cacheSize(context).round()
                            : null;
                    final cacheWidth = useCacheSizing && widthConstraint != null
                        ? widthConstraint.cacheSize(context).round()
                        : null;

                    // if (height == null && width == null) {
                    //   print(tag);
                    //   print(constraints);
                    // }
                    return CachedNetworkImage(
                        imageUrl: url!,
                        placeholder: (_, __) => placeholder,
                        errorWidget: (context, url, error) => placeholder,
                        height: heightConstraint,

                        // height,
                        width: widthConstraint,
                        // width,
                        fit: fit,
                        // fit: BoxFit.cover,
                        memCacheHeight: cacheHeight,

                        // (!useCacheSizing || height == null)
                        //     ? null
                        //     : (height!.cacheSize(context)).round(),
                        memCacheWidth: cacheWidth

                        // (!useCacheSizing || width == null)
                        //     ? null
                        //     : (width!.cacheSize(context)).round(),

                        // imageCacheHeight: height == null ? null : (height!).toInt(),
                        // imageCacheWidth: width == null ? null : (width!).toInt(),
                        );
                  })
                : CachedNetworkImage(
                    imageUrl: url!,
                    placeholder: (_, __) => placeholder,
                    errorWidget: (context, url, error) => placeholder,
                    height: height,

                    // height,
                    width: width,
                    // width,
                    fit: fit,
                    // fit: BoxFit.cover,
                    memCacheHeight: (!useCacheSizing || height == null)
                        ? null
                        : (height!.cacheSize(context)).round(),
                    memCacheWidth: (!useCacheSizing || width == null)
                        ? null
                        : (width!.cacheSize(context)).round(),

                    // imageCacheHeight: height == null ? null : (height!).toInt(),
                    // imageCacheWidth: width == null ? null : (width!).toInt(),
                  )
            : FadeInImage.assetNetwork(
                placeholder: placeholderAsset ?? Assets.logoImage,
                image: url!,
                fit: fit,
                height: height,
                width: width,
                placeholderCacheHeight: (!useCacheSizing || height == null)
                    ? null
                    : (height!.cacheSize(context)).round(),
                placeholderCacheWidth: (!useCacheSizing || width == null)
                    ? null
                    : (width!.cacheSize(context)).round(),
                imageCacheHeight: (!useCacheSizing || height == null)
                    ? null
                    : (height!.cacheSize(context)).round(),
                imageCacheWidth: (!useCacheSizing || width == null)
                    ? null
                    : (width!.cacheSize(context)).round(),
              );
  }
}

extension ImageExtension on num {
  int cacheSize(BuildContext context) {
    return (this * MediaQuery.of(context).devicePixelRatio).round();
  }
}

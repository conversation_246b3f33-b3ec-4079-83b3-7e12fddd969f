import 'package:ako_basma/styles/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../image/image_widget.dart';

class FeaturedSelectData {
  final int id;
  final String label;
  final String? image;
  final bool selected;

  const FeaturedSelectData({
    required this.id,
    required this.label,
    required this.image,
    this.selected = false,
  });
}

class FeaturedSelectRow extends StatelessWidget {
  const FeaturedSelectRow({
    super.key,
    this.loading = false,
    required this.loadingCount,
    required this.data,
    required this.onTap,
    this.padding,
    this.imageHeight = 80.0,
    this.imageWidth = 80.0,
    this.fit = BoxFit.contain,
  });

  final bool loading;
  final int loadingCount;
  final List<FeaturedSelectData> data;
  final void Function(int id, bool selected) onTap;
  final double imageWidth;
  final double imageHeight;
  final EdgeInsetsGeometry? padding;
  final BoxFit fit;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        padding: padding,
        child: IntrinsicHeight(
          child: Row(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: loading
                  ? List.generate(
                      loadingCount,
                      (index) => Container(
                        height: imageHeight + 30,
                        width: imageWidth - 10,
                        margin: const EdgeInsetsDirectional.symmetric(
                            horizontal: 5),
                        decoration: BoxDecoration(
                            color: colors(context)
                                .surfaceContainer
                                .withOpacity(0.5),
                            borderRadius: BorderRadius.circular(8)),
                      ).animate(onPlay: (c) {
                        c.repeat();
                      }).shimmer(duration: 2.seconds),
                    )
                  : data.map((data) {
                      return _buildCard(context, data).animate().fadeIn();
                    }).toList()),
        ));
  }

  Widget _buildCard(
    BuildContext context,
    FeaturedSelectData data,
  ) {
    return InkWell(
      onTap: () {
        onTap(data.id, data.selected);
      },
      child: AnimatedContainer(
        duration: 500.milliseconds,
        constraints: BoxConstraints(maxWidth: imageWidth + 20),
        decoration: BoxDecoration(
          border: Border.all(
            color:
                !data.selected ? Colors.transparent : colors(context).primary,
            width: 0.5,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsetsDirectional.symmetric(horizontal: 3),
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ImageContainer(
              url: data.image,
              height: imageHeight,
              width: imageWidth,
              fit: fit,
            ),
            const SizedBox(
              height: 5,
            ),
            Text(
              data.label,
              style: textStyles(context).labelMedium,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}

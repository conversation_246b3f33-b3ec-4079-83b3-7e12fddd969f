import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../styles/colors.dart';
import '../../styles/text.dart';
import '../../util/ui/popups.dart';

class ChipSelector extends StatelessWidget {
  const ChipSelector({
    super.key,
    this.items,
    this.selectedItems,
    this.onItemTap,
    this.taggedItems,
    this.selectedItemTags,
    this.onTaggedItemsTap,
    this.borderColor,
    this.selectedChipColor,
    this.selectedTextColor,
    this.validator,
    this.padding,
  })  : wrapMode = false,
        assert(
          (items != null && selectedItems != null && onItemTap != null) ||
              (taggedItems != null &&
                  selectedItemTags != null &&
                  onTaggedItemsTap != null),
          'Specify all the untagged arguments or tagged arguments.',
        );

  const ChipSelector.wrap({
    super.key,
    this.items,
    this.selectedItems,
    this.onItemTap,
    this.taggedItems,
    this.selectedItemTags,
    this.onTaggedItemsTap,
    this.borderColor,
    this.selectedChipColor,
    this.selectedTextColor,
    this.validator,
  })  : wrapMode = true,
        padding = null,
        assert(
          ((items != null && selectedItems != null && onItemTap != null) ||
              (taggedItems != null &&
                  selectedItemTags != null &&
                  onTaggedItemsTap != null)),
          'Specify all the untagged arguments or tagged arguments.',
        );

  final List<String>? items;
  final List<(String tag, String displayName)>? taggedItems;

  final List<String>? selectedItems;
  final List<String>? selectedItemTags;

  final void Function(String value)? onItemTap;
  final void Function(String tag)? onTaggedItemsTap;

  final bool wrapMode;
  final Color? borderColor;
  final Color? selectedChipColor;
  final EdgeInsetsGeometry? padding;
  final Color? selectedTextColor;
  final String? Function()? validator;

  @override
  Widget build(BuildContext context) {
    final children = taggedItems != null
        ? taggedItems!
            .map((e) =>
                _buildChip(context, e.$2, selectedItemTags!.contains(e.$1), () {
                  onTaggedItemsTap!(e.$1);
                }))
            .toList()
        : items!
            .map((e) => _buildChip(context, e, selectedItems!.contains(e), () {
                  onItemTap!(e);
                }))
            .toList();
    final content = wrapMode
        ? Wrap(
            spacing: 10,
            runSpacing: 8,
            children: children,
          )
        : Row(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  padding: padding,
                  child: Row(
                    children: children
                        .map((e) => Padding(
                              padding:
                                  const EdgeInsetsDirectional.only(end: 10),
                              child: e,
                            ))
                        .toList(),
                  ),
                ),
              ),
            ],
          );
    if (validator != null) {
      return FormField(validator: (_) {
        return validator!();
      }, builder: (_) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            content,
            if (_.hasError)
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(20, 10, 0, 0),
                child: customErrorText(context, _.errorText),
              )
          ],
        );
      });
    }
    return content;
  }

  Widget _buildChip(
      BuildContext context, String text, bool selected, void Function() onTap) {
    final style = textStyles(context).labelLarge!.copyWith(
          color:
              selected ? colors(context).onPrimary : colors(context).onSurface,
          fontWeight: selected ? FontWeight.w600 : null,
        );

    final color = selected
        ? selectedChipColor ?? colors(context).primary
        : colors(context).surfaceContainer;
    return InkWell(
      borderRadius: BorderRadius.circular(12),
      splashFactory: NoSplash.splashFactory,
      splashColor: Colors.transparent,
      overlayColor:
          MaterialStateColor.resolveWith((states) => Colors.transparent),
      onTap: () => onTap(),
      child: AnimatedContainer(
        duration: 250.milliseconds,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: color,
            border: borderColor == null
                ? null
                : Border.all(
                    color: selected ? color : borderColor!,
                    width: 1,
                  )),
        padding:
            const EdgeInsetsDirectional.symmetric(vertical: 10, horizontal: 20),
        child: Text(
          text,
          style: style,
        ),
      ),
    );
  }

  static Widget get skeleton {
    return SingleChildScrollView(
      padding: const EdgeInsetsDirectional.fromSTEB(20, 0, 24, 0),
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [100.0, 150.0, 70.0]
            .map((e) => Padding(
                  padding: const EdgeInsetsDirectional.only(end: 10),
                  child: Container(
                    width: e,
                    height: 30,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: Colors.blueGrey.withOpacity(0.2),
                    ),
                  ),
                ))
            .toList(),
      ),
    ).animate(onPlay: (c) {
      c.repeat();
    }).shimmer(duration: 2.seconds);
  }
}

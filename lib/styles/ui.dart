// import '../styles/colors.dart';
// import 'package:flutter/material.dart';

// class AppButtonStyle {
//   static ButtonStyle primaryButtonStyle(BuildContext context) =>
//       ElevatedButton.styleFrom(
//         backgroundColor: colors(context).primary,
//         foregroundColor: colors(context).onPrimary,
//         surfaceTintColor: colors(context).primary,
//         padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
//         textStyle: TextStyle(color: colors(context).onPrimary),
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(16),
//         ),
//         minimumSize: const Size.fromHeight(56),
//       );
//   static ButtonStyle filledIconButton(BuildContext context) =>
//       IconButton.styleFrom(
//         backgroundColor: colors(context).primary,
//         foregroundColor: colors(context).onPrimary,
//         surfaceTintColor: colors(context).primary,
//         // textStyle: AppTextStyle.primaryButton,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(16),
//         ),
//       );

//   static ButtonStyle primaryOutlinedButtonStyle(BuildContext context,
//           {Color? outlineColor}) =>
//       OutlinedButton.styleFrom(
//         backgroundColor: Colors.transparent,
//         foregroundColor: colors(context).onSurface,
//         side: BorderSide(
//           width: 0.6,
//           color: outlineColor ?? colors(context).outline,
//         ),
//         elevation: 0,
//         surfaceTintColor: Colors.transparent,
//         padding: const EdgeInsets.all(8),
//         // textStyle: AppTextStyle.primaryButton,
//         shape: const StadiumBorder(
//             // borderRadius: BorderRadius.circular(16),
//             ),
//       );
// }

import 'package:ako_basma/styles/theme.dart';

import '../styles/colors.dart';
import 'package:flutter/material.dart';

class AppButtonStyle {
  static ButtonStyle primaryButtonStyle(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return ButtonStyle(
      textStyle: WidgetStateTextStyle.resolveWith((states) {
        if (states.contains(WidgetState.disabled)) {
          return textStyles.buttonMedium.copyWith(
            color: colors.tertiaryText,
            height: 1.5,
          );
        }
        return textStyles.buttonMedium.copyWith(
          color: const Color(0xffffffff),
          height: 1.5,
        );
      }),
      elevation: WidgetStateProperty.resolveWith((states) {
        return states.contains(WidgetState.pressed) ? 4.0 : 0.0;
      }),
      backgroundColor: WidgetStateColor.resolveWith((states) {
        if (states.contains(WidgetState.disabled)) {
          return colors.disabled;
        }

        return colors.primary;
      }),
      foregroundColor: WidgetStateColor.resolveWith((states) {
        if (states.contains(WidgetState.disabled)) {
          return colors.tertiaryText;
        }
        return Colors.white;
      }),
      padding: WidgetStateProperty.all(
        const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      ),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      // minimumSize: WidgetStateProperty.all(
      //   const Size.fromHeight(56),
      // ),
      surfaceTintColor: WidgetStateProperty.all(colors.primary),
    );
    //  foregroundColor: Color(0xffffffff),
    //   surfaceTintColor: colors.primary,
    //   padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
    //   // textStyle: TextStyle(color: colors(context).onPrimary),
    //   shape: RoundedRectangleBorder(
    //     borderRadius: BorderRadius.circular(16),
    //   ),
    //   minimumSize: const Size.fromHeight(56),
  }

  static ButtonStyle filledIconButton(BuildContext context) =>
      IconButton.styleFrom(
        backgroundColor: colors(context).primary,
        foregroundColor: colors(context).onPrimary,
        surfaceTintColor: colors(context).primary,
        // textStyle: AppTextStyle.primaryButton,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      );

  static ButtonStyle primaryOutlinedButtonStyle(BuildContext context,
          {Color? outlineColor}) =>
      OutlinedButton.styleFrom(
        backgroundColor: Colors.transparent,
        foregroundColor: colors(context).onSurface,
        side: BorderSide(
          width: 0.6,
          color: outlineColor ?? colors(context).outline,
        ),
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        padding: const EdgeInsets.all(8),
        // textStyle: AppTextStyle.primaryButton,
        shape: const StadiumBorder(
            // borderRadius: BorderRadius.circular(16),
            ),
      );
}

import 'package:flutter/material.dart';

// Text styles based on Figma design
class AppTextStyles {
  // Headline styles (Semi Bold)
  static TextStyle headline1 = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 24, // Desktop: 32, Mobile: 24
    fontWeight: FontWeight.w600, // Semi Bold
    height: 1.0,
  );

  static TextStyle headline2 = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 20, // Desktop: 24, Mobile: 20
    fontWeight: FontWeight.w600, // Semi Bold
    height: 1.0,
  );

  /// IBM Plex Sans Arabic/18/Medium
  static TextStyle headline3 = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 18, // Desktop: 20, Mobile: 18
    fontWeight: FontWeight.w500, // Semi Bold
    height: 1.0,
    letterSpacing: 0,
  );

  // Title/large - IBM Plex Sans Arabic/16/SemiBold
  static TextStyle headline4 = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 16, // Desktop: 20, Mobile: 16
    fontWeight: FontWeight.w600, // Semi Bold
    height: 1.0,
    letterSpacing: 0,
  );

  // IBM Plex Sans Arabic/16/Regular
  static TextStyle body1 = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 16, // Desktop: 16/18, Mobile: 16/24
    fontWeight: FontWeight.w400, // Regular
    height: 1.0,
    letterSpacing: 0,
  );

  // IBM Plex Sans Arabic/14/Regular
  static TextStyle body2 = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontWeight: FontWeight.w400,
    fontSize: 14, // Desktop: 14/24, Mobile: 14/20
    height: 1.0,
    letterSpacing: 0,
  );

  static TextStyle body3 = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 12, // Desktop: 14/20, Mobile: 12/16
    fontWeight: FontWeight.w400, // Regular
    height: 1.0,
    letterSpacing: 0,
  );
  // Button styles (Regular)
  static TextStyle buttonNormal = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 16, // Desktop: 16/24, Mobile: 16/24
    fontWeight: FontWeight.w400, // Regular
    height: 1.0,
  );

  ///IBM Plex Sans Arabic/20/Medium(500)
  static TextStyle buttonMedium = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 20, // Desktop: 16/20, Mobile: 16/20
    fontWeight: FontWeight.w500, // Regular
    height: 1.0,
    letterSpacing: 0,
  );

  ///IBM Plex Sans Arabic/16/Medium(500)
  static TextStyle textButton = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 16, // Desktop: 16/20, Mobile: 16/20
    fontWeight: FontWeight.w500, // Medium
    height: 1.0,
    letterSpacing: 0,
  );

  static TextStyle buttonSmall = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 12, // Desktop: 14/16, Mobile: 14/16
    fontWeight: FontWeight.w400, // Regular
    height: 1.0,
  );

  // Field styles
  static TextStyle fieldText1 = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 16, // Desktop: 16/24, Mobile: 16/24
    fontWeight: FontWeight.w400, // Regular
    height: 1.0,
  );

  static TextStyle fieldText2 = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 12, // Desktop: 12/16, Mobile: 12/16
    fontWeight: FontWeight.w400, // Regular
    height: 1.0,
  );

  static TextStyle fieldLabel = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 14,
    fontWeight: FontWeight.w600, // Semi Bold
    height: 1.0,
  );

  static TextStyle fieldInput = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 16,
    fontWeight: FontWeight.w400, // Regular
    height: 1.0,
  );

  static TextStyle fieldHint = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 14,
    fontWeight: FontWeight.w400, // Regular
    color: Colors.grey,
    height: 1.0,
  );
}

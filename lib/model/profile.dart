import 'package:hive/hive.dart';
import 'dart:convert';
// part 'profile.g.dart';

Profile profileFromMap(String str) => Profile.fromMap(json.decode(str));

String profileToMap(Profile data) => json.encode(data.toMap());

@HiveType(typeId: 1)
class Profile {
  @HiveField(1)
  final int id;
  @HiveField(6)
  final String name;
  @HiveField(7)
  final String? email;
  @HiveField(8)
  final String? phone;
  @HiveField(9)
  final String? address;
  @HiveField(12)
  final String? postalCode;
  @HiveField(14)
  final String? profilePicture;

  Profile({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.address,
    required this.postalCode,
    required this.profilePicture,
  });

  factory Profile.fromMap(Map<String, dynamic> json) {
    return Profile(
      id: json["id"],
      name: json["name"],
      email: json["email"],
      phone: json["phone"],
      address: json["address"],
      postalCode: json["postal_code"],
      profilePicture: json["profile_picture"],
    );
  }

  Map<String, dynamic> toMap() => {
        "id": id,
        "name": name,
        "email": email,
        "phone": phone,
        "address": address,
        "postal_code": postalCode,
        "profile_picture": profilePicture,
      };

  String? get firstName {
    final nameParts = name.split(' ');
    return nameParts.firstOrNull;
  }

  String? get lastName {
    final nameParts = name.split(' ');
    if (nameParts.length <= 1) {
      return null;
    } else {
      return nameParts.sublist(1).join('');
    }
  }
}

import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Ako Basma';

  @override
  String get home => 'Home';

  @override
  String get chat => 'Chat';

  @override
  String get tasks => 'Tasks';

  @override
  String get profile => 'Profile';

  @override
  String get next => 'Next';

  @override
  String get checkYourMessages => 'Check Your Messages';

  @override
  String get verificationCode => 'We\'ve sent a verification code to your phone number';

  @override
  String get resendCode => 'Resend Code';

  @override
  String get email => 'Email';

  @override
  String get phone => 'Phone';

  @override
  String get close => 'Close';

  @override
  String get comments => 'Comments';

  @override
  String get expenseTitle => 'Expense Title';

  @override
  String get expensesAmount => '350,000';

  @override
  String get expensesDate => 'Nov 25, 2024';

  @override
  String get paid => 'Paid';

  @override
  String get search => 'Search';

  @override
  String get back => 'Back';

  @override
  String get loading => 'Loading...';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Delete';

  @override
  String get confirm => 'Confirm';

  @override
  String get success => 'Success';

  @override
  String get error => 'Error';

  @override
  String get noData => 'No data available';

  @override
  String get retry => 'Retry';

  @override
  String get settings => 'Settings';

  @override
  String get language => 'Language';

  @override
  String get notifications => 'Notifications';

  @override
  String get logout => 'Logout';

  @override
  String get welcome => 'Welcome';

  @override
  String get timeClock => 'Time Clock';

  @override
  String get workspace => 'Workspace';

  @override
  String get clockIn => 'Clock in';

  @override
  String get themeMode => 'Theme Mode';

  @override
  String get reportAProblem => 'Report A Problem';

  @override
  String get chatWithHR => 'Chat With HR';

  @override
  String get schedule => 'Schedule';

  @override
  String get salary => 'Salary';

  @override
  String get attachment => 'Attachment';

  @override
  String get resignationRequest => 'Resignation Request';

  @override
  String get conferenceCenter => 'Conference Center';

  @override
  String get conferenceDate => 'March 15, 2024';

  @override
  String get conferenceTime => '10:00 AM - 2:00 PM';

  @override
  String get conferenceLocation => 'Main Conference Hall';

  @override
  String get fileSelected => 'File Selected';

  @override
  String get otherDocuments => 'Other Documents';

  @override
  String get clickToUpload => 'Click to Upload';

  @override
  String get maxFileSize => 'Max file size 10MB';

  @override
  String get timeSheet => 'TimeSheet';

  @override
  String get filter => 'Filter';

  @override
  String get customDateRange => 'Custom Date Range';

  @override
  String get today => 'Today';

  @override
  String get location => 'Location';

  @override
  String get createTask => 'Create Task';

  @override
  String get createProject => 'Create Project';

  @override
  String get allProjects => 'All Projects';

  @override
  String get manageProjects => 'Manage Projects';

  @override
  String get noTasksFound => 'No Tasks Found';

  @override
  String get pleaseCreateATask => 'Please create a task';

  @override
  String get noProjectFound => 'No Project Found';

  @override
  String get pleaseCreateAProject => 'Please create a project';

  @override
  String get newGroup => 'New Group';

  @override
  String get newChannel => 'New Channel';

  @override
  String get ai => 'AI';

  @override
  String get name => 'Name';

  @override
  String get role => 'Role';

  @override
  String get designation => 'Designation';

  @override
  String get dob => 'Date of Birth';

  @override
  String get startDate => 'Start Date';

  @override
  String get editAccountInfo => 'Edit Account Info';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get emailAddress => 'Email Address';

  @override
  String get accountInfo => 'Account Info';

  @override
  String get otherInfo => 'Other Info';

  @override
  String get notEditable => 'Not Editable';

  @override
  String get notification => 'Notification';

  @override
  String get clockInTime => 'Clock in Time';

  @override
  String get totalShiftTime => 'Total Shift Time';

  @override
  String get overTime => 'Over Time';

  @override
  String get breakTime => 'Break Time';

  @override
  String get delay => 'Delay';

  @override
  String get about => 'About';

  @override
  String get aboutUs => 'About Us';

  @override
  String get termsOfUse => 'Terms of Use';

  @override
  String get selectDocument => 'Select Document';

  @override
  String get selectDocuments => 'Select Documents';

  @override
  String get feedback => 'Feedback';

  @override
  String get managersFeedback => 'Manager\'s Feedback';

  @override
  String get feedbackTime => 'Feedback Time';

  @override
  String get feedbackMessage => 'Feedback Message';

  @override
  String get other => 'Other';

  @override
  String get requestToChatWithHR => 'Request to Chat with HR';

  @override
  String get send => 'Send';

  @override
  String get hrGreeting => 'Hello! How can I help you today?';

  @override
  String get hrSampleMessage => 'I wanted to discuss my work schedule.';

  @override
  String get writeAMessage => 'Write a message...';

  @override
  String get hrName => 'HR Manager';

  @override
  String get hrLastSeen => 'Last seen 2 hours ago';

  @override
  String get hrApprovalMessage => 'Your request has been approved.';

  @override
  String get performance => 'Performance';

  @override
  String get completedTasks => 'Completed Tasks';

  @override
  String get timeOffTaken => 'Time Off Taken';

  @override
  String get expenses => 'Expenses';

  @override
  String get holidays => 'Holidays';

  @override
  String get waitingManagerApproval => 'Waiting for Manager Approval';

  @override
  String get canIGetMoreInfo => 'Can I get more info?';

  @override
  String get statusUpdate => 'Status Update';

  @override
  String get taskCompleted => 'Task Completed';

  @override
  String get needHelp => 'Need Help';

  @override
  String get approveTask => 'Approve Task';

  @override
  String get addComment => 'Add Comment';

  @override
  String get youCreatedGroup => 'You created this group';

  @override
  String get call => 'Call';

  @override
  String get clockOut => 'Clock Out';

  @override
  String get reject => 'Reject';

  @override
  String get approve => 'Approve';

  @override
  String get thisMonthsSalary => 'This Month\'s Salary';

  @override
  String get netSalary => 'Net Salary';

  @override
  String get confirmed => 'Confirmed';

  @override
  String get pending => 'Pending';

  @override
  String get groupName => 'Group Name';

  @override
  String get enterGroupName => 'Enter group name';

  @override
  String get groupDescription => 'Group Description';

  @override
  String get enterGroupDescription => 'Enter group description';

  @override
  String get create => 'Create';

  @override
  String get description => 'Description';

  @override
  String get taskDetails => 'Task Details';

  @override
  String get title => 'Title';

  @override
  String get amountIQD => 'Amount IQD';

  @override
  String get camera => 'Camera';

  @override
  String get record => 'Record';

  @override
  String get contact => 'Contact';

  @override
  String get gallery => 'Gallery';

  @override
  String get myLocation => 'My Location';

  @override
  String get document => 'Document';

  @override
  String get searching => 'Searching...';

  @override
  String get noContactsFound => 'No contacts found';

  @override
  String get assignee => 'Assignee';

  @override
  String get status => 'Status';

  @override
  String get department => 'Department';
}

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en')
  ];

  /// The name of the application
  ///
  /// In en, this message translates to:
  /// **'Ako Basma'**
  String get appName;

  /// Home tab label
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// Chat tab label
  ///
  /// In en, this message translates to:
  /// **'Chat'**
  String get chat;

  /// Tasks tab label
  ///
  /// In en, this message translates to:
  /// **'Tasks'**
  String get tasks;

  /// Profile tab label
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// Next button text
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// OTP screen title
  ///
  /// In en, this message translates to:
  /// **'Check Your Messages'**
  String get checkYourMessages;

  /// Verification code instruction
  ///
  /// In en, this message translates to:
  /// **'We\'ve sent a verification code to your phone number'**
  String get verificationCode;

  /// Resend verification code button
  ///
  /// In en, this message translates to:
  /// **'Resend Code'**
  String get resendCode;

  /// Email label
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// Phone label
  ///
  /// In en, this message translates to:
  /// **'Phone'**
  String get phone;

  /// Close button text
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// Comments section title
  ///
  /// In en, this message translates to:
  /// **'Comments'**
  String get comments;

  /// Expense title
  ///
  /// In en, this message translates to:
  /// **'Expense Title'**
  String get expenseTitle;

  /// Sample expense amount
  ///
  /// In en, this message translates to:
  /// **'350,000'**
  String get expensesAmount;

  /// Sample expense date
  ///
  /// In en, this message translates to:
  /// **'Nov 25, 2024'**
  String get expensesDate;

  /// Paid status
  ///
  /// In en, this message translates to:
  /// **'Paid'**
  String get paid;

  /// Search placeholder text
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// Back button text
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// Loading indicator text
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// Save button text
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// Cancel button text
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// Edit button text
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// Delete button text
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// Confirm button text
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// Success message
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// Error message
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No data message
  ///
  /// In en, this message translates to:
  /// **'No data available'**
  String get noData;

  /// Retry button text
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// Settings title
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Language setting
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// Notifications setting
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// Logout button text
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// Welcome greeting text
  ///
  /// In en, this message translates to:
  /// **'Welcome'**
  String get welcome;

  /// Time Clock tab
  ///
  /// In en, this message translates to:
  /// **'Time Clock'**
  String get timeClock;

  /// Workspace tab
  ///
  /// In en, this message translates to:
  /// **'Workspace'**
  String get workspace;

  /// Clock in button
  ///
  /// In en, this message translates to:
  /// **'Clock in'**
  String get clockIn;

  /// Theme mode setting
  ///
  /// In en, this message translates to:
  /// **'Theme Mode'**
  String get themeMode;

  /// Report problem option
  ///
  /// In en, this message translates to:
  /// **'Report A Problem'**
  String get reportAProblem;

  /// Chat with HR option
  ///
  /// In en, this message translates to:
  /// **'Chat With HR'**
  String get chatWithHR;

  /// Schedule title
  ///
  /// In en, this message translates to:
  /// **'Schedule'**
  String get schedule;

  /// Salary title
  ///
  /// In en, this message translates to:
  /// **'Salary'**
  String get salary;

  /// Attachment section title
  ///
  /// In en, this message translates to:
  /// **'Attachment'**
  String get attachment;

  /// Resignation request title
  ///
  /// In en, this message translates to:
  /// **'Resignation Request'**
  String get resignationRequest;

  /// Conference center label
  ///
  /// In en, this message translates to:
  /// **'Conference Center'**
  String get conferenceCenter;

  /// Conference date
  ///
  /// In en, this message translates to:
  /// **'March 15, 2024'**
  String get conferenceDate;

  /// Conference time
  ///
  /// In en, this message translates to:
  /// **'10:00 AM - 2:00 PM'**
  String get conferenceTime;

  /// Conference location
  ///
  /// In en, this message translates to:
  /// **'Main Conference Hall'**
  String get conferenceLocation;

  /// File selected message
  ///
  /// In en, this message translates to:
  /// **'File Selected'**
  String get fileSelected;

  /// Other documents label
  ///
  /// In en, this message translates to:
  /// **'Other Documents'**
  String get otherDocuments;

  /// Upload button text
  ///
  /// In en, this message translates to:
  /// **'Click to Upload'**
  String get clickToUpload;

  /// Maximum file size limit
  ///
  /// In en, this message translates to:
  /// **'Max file size 10MB'**
  String get maxFileSize;

  /// Timesheet title
  ///
  /// In en, this message translates to:
  /// **'TimeSheet'**
  String get timeSheet;

  /// Filter option
  ///
  /// In en, this message translates to:
  /// **'Filter'**
  String get filter;

  /// Custom date range filter
  ///
  /// In en, this message translates to:
  /// **'Custom Date Range'**
  String get customDateRange;

  /// Today filter
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// Location label
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// Create task button
  ///
  /// In en, this message translates to:
  /// **'Create Task'**
  String get createTask;

  /// Create project button
  ///
  /// In en, this message translates to:
  /// **'Create Project'**
  String get createProject;

  /// All projects filter
  ///
  /// In en, this message translates to:
  /// **'All Projects'**
  String get allProjects;

  /// Manage projects option
  ///
  /// In en, this message translates to:
  /// **'Manage Projects'**
  String get manageProjects;

  /// No tasks message
  ///
  /// In en, this message translates to:
  /// **'No Tasks Found'**
  String get noTasksFound;

  /// Create task prompt
  ///
  /// In en, this message translates to:
  /// **'Please create a task'**
  String get pleaseCreateATask;

  /// No project message
  ///
  /// In en, this message translates to:
  /// **'No Project Found'**
  String get noProjectFound;

  /// Create project prompt
  ///
  /// In en, this message translates to:
  /// **'Please create a project'**
  String get pleaseCreateAProject;

  /// New group option
  ///
  /// In en, this message translates to:
  /// **'New Group'**
  String get newGroup;

  /// New channel option
  ///
  /// In en, this message translates to:
  /// **'New Channel'**
  String get newChannel;

  /// AI label
  ///
  /// In en, this message translates to:
  /// **'AI'**
  String get ai;

  /// Name field
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// Role field
  ///
  /// In en, this message translates to:
  /// **'Role'**
  String get role;

  /// Designation field
  ///
  /// In en, this message translates to:
  /// **'Designation'**
  String get designation;

  /// Date of birth
  ///
  /// In en, this message translates to:
  /// **'Date of Birth'**
  String get dob;

  /// Start date
  ///
  /// In en, this message translates to:
  /// **'Start Date'**
  String get startDate;

  /// Edit account info button
  ///
  /// In en, this message translates to:
  /// **'Edit Account Info'**
  String get editAccountInfo;

  /// Phone number field
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneNumber;

  /// Email address field
  ///
  /// In en, this message translates to:
  /// **'Email Address'**
  String get emailAddress;

  /// Account info section
  ///
  /// In en, this message translates to:
  /// **'Account Info'**
  String get accountInfo;

  /// Other info section
  ///
  /// In en, this message translates to:
  /// **'Other Info'**
  String get otherInfo;

  /// Not editable message
  ///
  /// In en, this message translates to:
  /// **'Not Editable'**
  String get notEditable;

  /// Notification title
  ///
  /// In en, this message translates to:
  /// **'Notification'**
  String get notification;

  /// Clock in time label
  ///
  /// In en, this message translates to:
  /// **'Clock in Time'**
  String get clockInTime;

  /// Total shift time label
  ///
  /// In en, this message translates to:
  /// **'Total Shift Time'**
  String get totalShiftTime;

  /// Overtime label
  ///
  /// In en, this message translates to:
  /// **'Over Time'**
  String get overTime;

  /// Break time label
  ///
  /// In en, this message translates to:
  /// **'Break Time'**
  String get breakTime;

  /// Delay label
  ///
  /// In en, this message translates to:
  /// **'Delay'**
  String get delay;

  /// About section
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get about;

  /// About us option
  ///
  /// In en, this message translates to:
  /// **'About Us'**
  String get aboutUs;

  /// Terms of use option
  ///
  /// In en, this message translates to:
  /// **'Terms of Use'**
  String get termsOfUse;

  /// Select document title
  ///
  /// In en, this message translates to:
  /// **'Select Document'**
  String get selectDocument;

  /// Select documents title
  ///
  /// In en, this message translates to:
  /// **'Select Documents'**
  String get selectDocuments;

  /// Feedback section
  ///
  /// In en, this message translates to:
  /// **'Feedback'**
  String get feedback;

  /// Manager feedback label
  ///
  /// In en, this message translates to:
  /// **'Manager\'s Feedback'**
  String get managersFeedback;

  /// Feedback time label
  ///
  /// In en, this message translates to:
  /// **'Feedback Time'**
  String get feedbackTime;

  /// Feedback message label
  ///
  /// In en, this message translates to:
  /// **'Feedback Message'**
  String get feedbackMessage;

  /// Other section
  ///
  /// In en, this message translates to:
  /// **'Other'**
  String get other;

  /// HR chat request title
  ///
  /// In en, this message translates to:
  /// **'Request to Chat with HR'**
  String get requestToChatWithHR;

  /// Send button
  ///
  /// In en, this message translates to:
  /// **'Send'**
  String get send;

  /// HR greeting message
  ///
  /// In en, this message translates to:
  /// **'Hello! How can I help you today?'**
  String get hrGreeting;

  /// HR sample message
  ///
  /// In en, this message translates to:
  /// **'I wanted to discuss my work schedule.'**
  String get hrSampleMessage;

  /// Message input placeholder
  ///
  /// In en, this message translates to:
  /// **'Write a message...'**
  String get writeAMessage;

  /// HR manager name
  ///
  /// In en, this message translates to:
  /// **'HR Manager'**
  String get hrName;

  /// HR last seen status
  ///
  /// In en, this message translates to:
  /// **'Last seen 2 hours ago'**
  String get hrLastSeen;

  /// HR approval message
  ///
  /// In en, this message translates to:
  /// **'Your request has been approved.'**
  String get hrApprovalMessage;

  /// Performance section
  ///
  /// In en, this message translates to:
  /// **'Performance'**
  String get performance;

  /// Completed tasks metric
  ///
  /// In en, this message translates to:
  /// **'Completed Tasks'**
  String get completedTasks;

  /// Time off taken metric
  ///
  /// In en, this message translates to:
  /// **'Time Off Taken'**
  String get timeOffTaken;

  /// Expenses section
  ///
  /// In en, this message translates to:
  /// **'Expenses'**
  String get expenses;

  /// Holidays metric
  ///
  /// In en, this message translates to:
  /// **'Holidays'**
  String get holidays;

  /// Pending approval status
  ///
  /// In en, this message translates to:
  /// **'Waiting for Manager Approval'**
  String get waitingManagerApproval;

  /// Request info comment
  ///
  /// In en, this message translates to:
  /// **'Can I get more info?'**
  String get canIGetMoreInfo;

  /// Status update comment
  ///
  /// In en, this message translates to:
  /// **'Status Update'**
  String get statusUpdate;

  /// Task completed comment
  ///
  /// In en, this message translates to:
  /// **'Task Completed'**
  String get taskCompleted;

  /// Need help comment
  ///
  /// In en, this message translates to:
  /// **'Need Help'**
  String get needHelp;

  /// Approve task comment
  ///
  /// In en, this message translates to:
  /// **'Approve Task'**
  String get approveTask;

  /// Add comment button
  ///
  /// In en, this message translates to:
  /// **'Add Comment'**
  String get addComment;

  /// Group creation message
  ///
  /// In en, this message translates to:
  /// **'You created this group'**
  String get youCreatedGroup;

  /// Call action
  ///
  /// In en, this message translates to:
  /// **'Call'**
  String get call;

  /// Clock out button
  ///
  /// In en, this message translates to:
  /// **'Clock Out'**
  String get clockOut;

  /// Reject button
  ///
  /// In en, this message translates to:
  /// **'Reject'**
  String get reject;

  /// Approve button
  ///
  /// In en, this message translates to:
  /// **'Approve'**
  String get approve;

  /// Current month salary title
  ///
  /// In en, this message translates to:
  /// **'This Month\'s Salary'**
  String get thisMonthsSalary;

  /// Net salary label
  ///
  /// In en, this message translates to:
  /// **'Net Salary'**
  String get netSalary;

  /// Confirmed status
  ///
  /// In en, this message translates to:
  /// **'Confirmed'**
  String get confirmed;

  /// Pending status
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get pending;

  /// Group name field
  ///
  /// In en, this message translates to:
  /// **'Group Name'**
  String get groupName;

  /// Group name placeholder
  ///
  /// In en, this message translates to:
  /// **'Enter group name'**
  String get enterGroupName;

  /// Group description field
  ///
  /// In en, this message translates to:
  /// **'Group Description'**
  String get groupDescription;

  /// Group description placeholder
  ///
  /// In en, this message translates to:
  /// **'Enter group description'**
  String get enterGroupDescription;

  /// Create button text
  ///
  /// In en, this message translates to:
  /// **'Create'**
  String get create;

  /// Description label
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// Task details header
  ///
  /// In en, this message translates to:
  /// **'Task Details'**
  String get taskDetails;

  /// Title field label
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get title;

  /// Amount in IQD currency
  ///
  /// In en, this message translates to:
  /// **'Amount IQD'**
  String get amountIQD;

  /// Camera option
  ///
  /// In en, this message translates to:
  /// **'Camera'**
  String get camera;

  /// Record option
  ///
  /// In en, this message translates to:
  /// **'Record'**
  String get record;

  /// Contact option
  ///
  /// In en, this message translates to:
  /// **'Contact'**
  String get contact;

  /// Gallery option
  ///
  /// In en, this message translates to:
  /// **'Gallery'**
  String get gallery;

  /// Location option
  ///
  /// In en, this message translates to:
  /// **'My Location'**
  String get myLocation;

  /// Document option
  ///
  /// In en, this message translates to:
  /// **'Document'**
  String get document;

  /// Search placeholder text
  ///
  /// In en, this message translates to:
  /// **'Searching...'**
  String get searching;

  /// Empty state message
  ///
  /// In en, this message translates to:
  /// **'No contacts found'**
  String get noContactsFound;

  /// Assignee field label
  ///
  /// In en, this message translates to:
  /// **'Assignee'**
  String get assignee;

  /// Status field label
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// Department field label
  ///
  /// In en, this message translates to:
  /// **'Department'**
  String get department;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['ar', 'en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar': return AppLocalizationsAr();
    case 'en': return AppLocalizationsEn();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}

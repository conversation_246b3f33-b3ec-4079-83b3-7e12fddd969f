import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appName => 'أكو بصمة';

  @override
  String get home => 'الرئيسية';

  @override
  String get chat => 'المحادثة';

  @override
  String get tasks => 'المهام';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get next => 'التالي';

  @override
  String get checkYourMessages => 'تحقق من رسائلك';

  @override
  String get verificationCode => 'لقد أرسلنا رمز التحقق إلى رقم هاتفك';

  @override
  String get resendCode => 'إعادة إرسال الرمز';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get phone => 'الهاتف';

  @override
  String get close => 'إغلاق';

  @override
  String get comments => 'التعليقات';

  @override
  String get expenseTitle => 'عنوان المصروف';

  @override
  String get expensesAmount => '350,000';

  @override
  String get expensesDate => '25 نوفمبر 2024';

  @override
  String get paid => 'مدفوع';

  @override
  String get search => 'البحث';

  @override
  String get back => 'رجوع';

  @override
  String get loading => 'جارٍ التحميل...';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get edit => 'تحرير';

  @override
  String get delete => 'حذف';

  @override
  String get confirm => 'تأكيد';

  @override
  String get success => 'نجح';

  @override
  String get error => 'خطأ';

  @override
  String get noData => 'لا توجد بيانات متاحة';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get settings => 'الإعدادات';

  @override
  String get language => 'اللغة';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get welcome => 'أهلاً وسهلاً';

  @override
  String get timeClock => 'ساعة العمل';

  @override
  String get workspace => 'مساحة العمل';

  @override
  String get clockIn => 'تسجيل الحضور';

  @override
  String get themeMode => 'وضع المظهر';

  @override
  String get reportAProblem => 'الإبلاغ عن مشكلة';

  @override
  String get chatWithHR => 'محادثة مع الموارد البشرية';

  @override
  String get schedule => 'الجدولة';

  @override
  String get salary => 'الراتب';

  @override
  String get attachment => 'المرفق';

  @override
  String get resignationRequest => 'طلب الاستقالة';

  @override
  String get conferenceCenter => 'مركز المؤتمرات';

  @override
  String get conferenceDate => '15 مارس 2024';

  @override
  String get conferenceTime => '10:00 صباحاً - 2:00 بعد الظهر';

  @override
  String get conferenceLocation => 'قاعة المؤتمرات الرئيسية';

  @override
  String get fileSelected => 'تم اختيار الملف';

  @override
  String get otherDocuments => 'مستندات أخرى';

  @override
  String get clickToUpload => 'انقر للرفع';

  @override
  String get maxFileSize => 'حد أقصى للملف 10MB';

  @override
  String get timeSheet => 'كشف الوقت';

  @override
  String get filter => 'تصفية';

  @override
  String get customDateRange => 'نطاق تاريخ مخصص';

  @override
  String get today => 'اليوم';

  @override
  String get location => 'الموقع';

  @override
  String get createTask => 'إنشاء مهمة';

  @override
  String get createProject => 'إنشاء مشروع';

  @override
  String get allProjects => 'جميع المشاريع';

  @override
  String get manageProjects => 'إدارة المشاريع';

  @override
  String get noTasksFound => 'لم يتم العثور على مهام';

  @override
  String get pleaseCreateATask => 'يرجى إنشاء مهمة';

  @override
  String get noProjectFound => 'لم يتم العثور على مشروع';

  @override
  String get pleaseCreateAProject => 'يرجى إنشاء مشروع';

  @override
  String get newGroup => 'مجموعة جديدة';

  @override
  String get newChannel => 'قناة جديدة';

  @override
  String get ai => 'الذكاء الاصطناعي';

  @override
  String get name => 'الاسم';

  @override
  String get role => 'الدور';

  @override
  String get designation => 'المسمى الوظيفي';

  @override
  String get dob => 'تاريخ الميلاد';

  @override
  String get startDate => 'تاريخ البدء';

  @override
  String get editAccountInfo => 'تحرير معلومات الحساب';

  @override
  String get phoneNumber => 'رقم الهاتف';

  @override
  String get emailAddress => 'عنوان البريد الإلكتروني';

  @override
  String get accountInfo => 'معلومات الحساب';

  @override
  String get otherInfo => 'معلومات أخرى';

  @override
  String get notEditable => 'غير قابل للتحرير';

  @override
  String get notification => 'الإشعار';

  @override
  String get clockInTime => 'وقت تسجيل الحضور';

  @override
  String get totalShiftTime => 'إجمالي وقت الوردية';

  @override
  String get overTime => 'الوقت الإضافي';

  @override
  String get breakTime => 'وقت الاستراحة';

  @override
  String get delay => 'التأخير';

  @override
  String get about => 'حول';

  @override
  String get aboutUs => 'عنا';

  @override
  String get termsOfUse => 'شروط الاستخدام';

  @override
  String get selectDocument => 'اختيار مستند';

  @override
  String get selectDocuments => 'اختيار مستندات';

  @override
  String get feedback => 'التعليقات';

  @override
  String get managersFeedback => 'تعليقات المدير';

  @override
  String get feedbackTime => 'وقت التعليق';

  @override
  String get feedbackMessage => 'رسالة التعليق';

  @override
  String get other => 'أخرى';

  @override
  String get requestToChatWithHR => 'طلب محادثة مع الموارد البشرية';

  @override
  String get send => 'إرسال';

  @override
  String get hrGreeting => 'مرحباً! كيف يمكنني مساعدتك اليوم؟';

  @override
  String get hrSampleMessage => 'أردت مناقشة جدول عملي.';

  @override
  String get writeAMessage => 'اكتب رسالة...';

  @override
  String get hrName => 'مدير الموارد البشرية';

  @override
  String get hrLastSeen => 'آخر ظهور منذ ساعتين';

  @override
  String get hrApprovalMessage => 'تم الموافقة على طلبك.';

  @override
  String get performance => 'الأداء';

  @override
  String get completedTasks => 'المهام المكتملة';

  @override
  String get timeOffTaken => 'الإجازات المأخوذة';

  @override
  String get expenses => 'المصروفات';

  @override
  String get holidays => 'العطل';

  @override
  String get waitingManagerApproval => 'في انتظار موافقة المدير';

  @override
  String get canIGetMoreInfo => 'هل يمكنني الحصول على مزيد من المعلومات؟';

  @override
  String get statusUpdate => 'تحديث الحالة';

  @override
  String get taskCompleted => 'تمت المهمة';

  @override
  String get needHelp => 'أحتاج مساعدة';

  @override
  String get approveTask => 'الموافقة على المهمة';

  @override
  String get addComment => 'إضافة تعليق';

  @override
  String get youCreatedGroup => 'لقد أنشأت هذه المجموعة';

  @override
  String get call => 'اتصال';

  @override
  String get clockOut => 'تسجيل الخروج';

  @override
  String get reject => 'رفض';

  @override
  String get approve => 'موافق';

  @override
  String get thisMonthsSalary => 'راتب هذا الشهر';

  @override
  String get netSalary => 'الراتب الصافي';

  @override
  String get confirmed => 'مؤكد';

  @override
  String get pending => 'في الانتظار';

  @override
  String get groupName => 'اسم المجموعة';

  @override
  String get enterGroupName => 'أدخل اسم المجموعة';

  @override
  String get groupDescription => 'وصف المجموعة';

  @override
  String get enterGroupDescription => 'أدخل وصف المجموعة';

  @override
  String get create => 'إنشاء';

  @override
  String get description => 'الوصف';

  @override
  String get taskDetails => 'تفاصيل المهمة';

  @override
  String get title => 'العنوان';

  @override
  String get amountIQD => 'المبلغ (دينار عراقي)';

  @override
  String get camera => 'الكاميرا';

  @override
  String get record => 'تسجيل';

  @override
  String get contact => 'جهة اتصال';

  @override
  String get gallery => 'المعرض';

  @override
  String get myLocation => 'موقعي';

  @override
  String get document => 'مستند';

  @override
  String get searching => 'البحث...';

  @override
  String get noContactsFound => 'لم يتم العثور على جهات اتصال';

  @override
  String get assignee => 'المكلف';

  @override
  String get status => 'الحالة';

  @override
  String get department => 'القسم';
}

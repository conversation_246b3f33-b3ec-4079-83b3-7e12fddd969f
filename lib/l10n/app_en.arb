{"@@locale": "en", "appName": "<PERSON><PERSON>", "@appName": {"description": "The name of the application"}, "home": "Home", "@home": {"description": "Home tab label"}, "chat": "Cha<PERSON>", "@chat": {"description": "Chat tab label"}, "tasks": "Tasks", "@tasks": {"description": "Tasks tab label"}, "profile": "Profile", "@profile": {"description": "Profile tab label"}, "next": "Next", "@next": {"description": "Next button text"}, "checkYourMessages": "Check Your Messages", "@checkYourMessages": {"description": "OTP screen title"}, "verificationCode": "We've sent a verification code to your phone number", "@verificationCode": {"description": "Verification code instruction"}, "resendCode": "Resend Code", "@resendCode": {"description": "Resend verification code button"}, "email": "Email", "@email": {"description": "Email label"}, "phone": "Phone", "@phone": {"description": "Phone label"}, "close": "Close", "@close": {"description": "Close button text"}, "comments": "Comments", "@comments": {"description": "Comments section title"}, "expenseTitle": "Expense Title", "@expenseTitle": {"description": "Expense title"}, "expensesAmount": "350,000", "@expensesAmount": {"description": "Sample expense amount"}, "expensesDate": "Nov 25, 2024", "@expensesDate": {"description": "Sample expense date"}, "paid": "Paid", "@paid": {"description": "Paid status"}, "search": "Search", "@search": {"description": "Search placeholder text"}, "back": "Back", "@back": {"description": "Back button text"}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "save": "Save", "@save": {"description": "Save button text"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "edit": "Edit", "@edit": {"description": "Edit button text"}, "delete": "Delete", "@delete": {"description": "Delete button text"}, "confirm": "Confirm", "@confirm": {"description": "Confirm button text"}, "success": "Success", "@success": {"description": "Success message"}, "error": "Error", "@error": {"description": "Error message"}, "noData": "No data available", "@noData": {"description": "No data message"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "settings": "Settings", "@settings": {"description": "Settings title"}, "language": "Language", "@language": {"description": "Language setting"}, "notifications": "Notifications", "@notifications": {"description": "Notifications setting"}, "logout": "Logout", "@logout": {"description": "Logout button text"}, "welcome": "Welcome", "@welcome": {"description": "Welcome greeting text"}, "timeClock": "Time Clock", "@timeClock": {"description": "Time Clock tab"}, "workspace": "Workspace", "@workspace": {"description": "Workspace tab"}, "clockIn": "Clock in", "@clockIn": {"description": "Clock in button"}, "themeMode": "Theme Mode", "@themeMode": {"description": "Theme mode setting"}, "reportAProblem": "Report A Problem", "@reportAProblem": {"description": "Report problem option"}, "chatWithHR": "Chat With HR", "@chatWithHR": {"description": "Chat with HR option"}, "schedule": "Schedule", "@schedule": {"description": "Schedule title"}, "salary": "Salary", "@salary": {"description": "Salary title"}, "attachment": "Attachment", "@attachment": {"description": "Attachment section title"}, "resignationRequest": "Resignation Request", "@resignationRequest": {"description": "Resignation request title"}, "conferenceCenter": "Conference Center", "@conferenceCenter": {"description": "Conference center label"}, "conferenceDate": "March 15, 2024", "@conferenceDate": {"description": "Conference date"}, "conferenceTime": "10:00 AM - 2:00 PM", "@conferenceTime": {"description": "Conference time"}, "conferenceLocation": "Main Conference Hall", "@conferenceLocation": {"description": "Conference location"}, "fileSelected": "File Selected", "@fileSelected": {"description": "File selected message"}, "otherDocuments": "Other Documents", "@otherDocuments": {"description": "Other documents label"}, "clickToUpload": "Click to Upload", "@clickToUpload": {"description": "Upload button text"}, "maxFileSize": "Max file size 10MB", "@maxFileSize": {"description": "Maximum file size limit"}, "timeSheet": "TimeSheet", "@timeSheet": {"description": "Timesheet title"}, "filter": "Filter", "@filter": {"description": "Filter option"}, "customDateRange": "Custom Date Range", "@customDateRange": {"description": "Custom date range filter"}, "today": "Today", "@today": {"description": "Today filter"}, "location": "Location", "@location": {"description": "Location label"}, "createTask": "Create Task", "@createTask": {"description": "Create task button"}, "createProject": "Create Project", "@createProject": {"description": "Create project button"}, "allProjects": "All Projects", "@allProjects": {"description": "All projects filter"}, "manageProjects": "Manage Projects", "@manageProjects": {"description": "Manage projects option"}, "noTasksFound": "No Tasks Found", "@noTasksFound": {"description": "No tasks message"}, "pleaseCreateATask": "Please create a task", "@pleaseCreateATask": {"description": "Create task prompt"}, "noProjectFound": "No Project Found", "@noProjectFound": {"description": "No project message"}, "pleaseCreateAProject": "Please create a project", "@pleaseCreateAProject": {"description": "Create project prompt"}, "newGroup": "New Group", "@newGroup": {"description": "New group option"}, "newChannel": "New Channel", "@newChannel": {"description": "New channel option"}, "ai": "AI", "@ai": {"description": "AI label"}, "name": "Name", "@name": {"description": "Name field"}, "role": "Role", "@role": {"description": "Role field"}, "designation": "Designation", "@designation": {"description": "Designation field"}, "dob": "Date of Birth", "@dob": {"description": "Date of birth"}, "startDate": "Start Date", "@startDate": {"description": "Start date"}, "editAccountInfo": "Edit Account Info", "@editAccountInfo": {"description": "Edit account info button"}, "phoneNumber": "Phone Number", "@phoneNumber": {"description": "Phone number field"}, "emailAddress": "Email Address", "@emailAddress": {"description": "Email address field"}, "accountInfo": "Account Info", "@accountInfo": {"description": "Account info section"}, "otherInfo": "Other Info", "@otherInfo": {"description": "Other info section"}, "notEditable": "Not Editable", "@notEditable": {"description": "Not editable message"}, "notification": "Notification", "@notification": {"description": "Notification title"}, "clockInTime": "Clock in Time", "@clockInTime": {"description": "Clock in time label"}, "totalShiftTime": "Total Shift Time", "@totalShiftTime": {"description": "Total shift time label"}, "overTime": "Over Time", "@overTime": {"description": "Overtime label"}, "breakTime": "Break Time", "@breakTime": {"description": "Break time label"}, "delay": "Delay", "@delay": {"description": "Delay label"}, "about": "About", "@about": {"description": "About section"}, "aboutUs": "About Us", "@aboutUs": {"description": "About us option"}, "termsOfUse": "Terms of Use", "@termsOfUse": {"description": "Terms of use option"}, "selectDocument": "Select Document", "@selectDocument": {"description": "Select document title"}, "selectDocuments": "Select Documents", "@selectDocuments": {"description": "Select documents title"}, "feedback": "<PERSON><PERSON><PERSON>", "@feedback": {"description": "Feedback section"}, "managersFeedback": "Manager's <PERSON><PERSON><PERSON>", "@managersFeedback": {"description": "Manager feedback label"}, "feedbackTime": "Feedback Time", "@feedbackTime": {"description": "Feedback time label"}, "feedbackMessage": "Feedback Message", "@feedbackMessage": {"description": "Feedback message label"}, "other": "Other", "@other": {"description": "Other section"}, "requestToChatWithHR": "Request to Chat with HR", "@requestToChatWithHR": {"description": "HR chat request title"}, "send": "Send", "@send": {"description": "Send button"}, "hrGreeting": "Hello! How can I help you today?", "@hrGreeting": {"description": "HR greeting message"}, "hrSampleMessage": "I wanted to discuss my work schedule.", "@hrSampleMessage": {"description": "HR sample message"}, "writeAMessage": "Write a message...", "@writeAMessage": {"description": "Message input placeholder"}, "hrName": "HR Manager", "@hrName": {"description": "HR manager name"}, "hrLastSeen": "Last seen 2 hours ago", "@hrLastSeen": {"description": "HR last seen status"}, "hrApprovalMessage": "Your request has been approved.", "@hrApprovalMessage": {"description": "HR approval message"}, "performance": "Performance", "@performance": {"description": "Performance section"}, "completedTasks": "Completed Tasks", "@completedTasks": {"description": "Completed tasks metric"}, "timeOffTaken": "Time Off Taken", "@timeOffTaken": {"description": "Time off taken metric"}, "expenses": "Expenses", "@expenses": {"description": "Expenses section"}, "holidays": "Holidays", "@holidays": {"description": "Holidays metric"}, "waitingManagerApproval": "Waiting for Manager <PERSON><PERSON><PERSON><PERSON>", "@waitingManagerApproval": {"description": "Pending approval status"}, "canIGetMoreInfo": "Can I get more info?", "@canIGetMoreInfo": {"description": "Request info comment"}, "statusUpdate": "Status Update", "@statusUpdate": {"description": "Status update comment"}, "taskCompleted": "Task Completed", "@taskCompleted": {"description": "Task completed comment"}, "needHelp": "Need Help", "@needHelp": {"description": "Need help comment"}, "approveTask": "Approve Task", "@approveTask": {"description": "Approve task comment"}, "addComment": "Add Comment", "@addComment": {"description": "Add comment button"}, "youCreatedGroup": "You created this group", "@youCreatedGroup": {"description": "Group creation message"}, "call": "Call", "@call": {"description": "Call action"}, "clockOut": "Clock Out", "@clockOut": {"description": "Clock out button"}, "reject": "Reject", "@reject": {"description": "Reject button"}, "approve": "Approve", "@approve": {"description": "Approve button"}, "thisMonthsSalary": "This Month's Salary", "@thisMonthsSalary": {"description": "Current month salary title"}, "netSalary": "Net Salary", "@netSalary": {"description": "Net salary label"}, "confirmed": "Confirmed", "@confirmed": {"description": "Confirmed status"}, "pending": "Pending", "@pending": {"description": "Pending status"}, "groupName": "Group Name", "@groupName": {"description": "Group name field"}, "enterGroupName": "Enter group name", "@enterGroupName": {"description": "Group name placeholder"}, "groupDescription": "Group Description", "@groupDescription": {"description": "Group description field"}, "enterGroupDescription": "Enter group description", "@enterGroupDescription": {"description": "Group description placeholder"}, "create": "Create", "@create": {"description": "Create button text"}, "description": "Description", "@description": {"description": "Description label"}, "taskDetails": "Task Details", "@taskDetails": {"description": "Task details header"}, "title": "Title", "@title": {"description": "Title field label"}, "amountIQD": "Amount IQD", "@amountIQD": {"description": "Amount in IQD currency"}, "camera": "Camera", "@camera": {"description": "Camera option"}, "record": "Record", "@record": {"description": "Record option"}, "contact": "Contact", "@contact": {"description": "Contact option"}, "gallery": "Gallery", "@gallery": {"description": "Gallery option"}, "myLocation": "My Location", "@myLocation": {"description": "Location option"}, "document": "Document", "@document": {"description": "Document option"}, "searching": "Searching...", "@searching": {"description": "Search placeholder text"}, "noContactsFound": "No contacts found", "@noContactsFound": {"description": "Empty state message"}, "assignee": "Assignee", "@assignee": {"description": "Assignee field label"}, "status": "Status", "@status": {"description": "Status field label"}, "department": "Department", "@department": {"description": "Department field label"}}
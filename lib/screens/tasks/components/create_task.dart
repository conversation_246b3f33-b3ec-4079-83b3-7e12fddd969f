import 'package:ako_basma/constants/assets.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/tasks/components/add_task_popup.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

class CreateTask extends StatefulWidget {
  const CreateTask({super.key});

  @override
  State<CreateTask> createState() => _CreateTaskState();
}

class _CreateTaskState extends State<CreateTask> {
  String selectedProject = '';

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    // Initialize selected project if empty
    if (selectedProject.isEmpty) {
      selectedProject = localization.allProjects;
    }

    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header with project selector
            Container(
              padding: const EdgeInsetsDirectional.all(16),
              child: Row(
                children: [
                  // Project selector dropdown
                  Expanded(
                    child: Container(
                      height: 40,
                      decoration: BoxDecoration(
                        border: Border.all(color: colors.strokeColor),
                        borderRadius: BorderRadius.circular(8),
                        color: colors.backgroundContainer,
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsetsDirectional.symmetric(
                                horizontal: 12),
                            child: Text(
                              selectedProject,
                              style: textStyles.body2.copyWith(
                                color: colors.secondaryText,
                              ),
                            ),
                          ),
                          const Spacer(),
                          Container(
                            padding: const EdgeInsetsDirectional.only(end: 12),
                            child: Icon(
                              Icons.keyboard_arrow_down,
                              color: colors.secondaryText,
                              size: 20,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Project settings button
                  Container(
                    margin: const EdgeInsetsDirectional.only(start: 12),
                    child: GestureDetector(
                      onTap: () {
                        // Handle project settings
                      },
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: colors.backgroundContainer,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: colors.strokeColor,
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          Icons.settings_outlined,
                          color: colors.primaryText,
                          size: 20,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Project management section
            Container(
              margin: const EdgeInsetsDirectional.symmetric(horizontal: 16),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      padding:
                          const EdgeInsetsDirectional.symmetric(vertical: 8),
                      child: GestureDetector(
                        onTap: () {
                          // Handle manage projects
                        },
                        child: Container(
                          alignment: AlignmentDirectional.centerStart,
                          child: Text(
                            localization.manageProjects,
                            style: textStyles.body2.copyWith(
                              color: colors.primary,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Main content area
            Expanded(
              child: Container(
                padding: const EdgeInsetsDirectional.symmetric(horizontal: 16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Empty state illustration
                    Container(
                      width: 200,
                      height: 200,
                      decoration: const BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage(Assets.createTaskImage),
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),

                    Container(height: 24),

                    // No tasks message
                    Text(
                      localization.noTasksFound,
                      style: textStyles.headline3.copyWith(
                        color: colors.primaryText,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    Container(height: 8),

                    // Create task prompt
                    Text(
                      localization.pleaseCreateATask,
                      style: textStyles.body2.copyWith(
                        color: colors.secondaryText,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    Container(height: 32),

                    // Create task button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            builder: (context) => const AddTaskPopup(),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: colors.primary,
                          foregroundColor: Colors.white,
                          minimumSize: const Size.fromHeight(48),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          localization.createTask,
                          style: textStyles.body.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

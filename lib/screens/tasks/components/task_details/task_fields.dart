import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'dropdown_field.dart';
import 'assignee_field.dart';

class TaskFields extends StatelessWidget {
  const TaskFields({super.key});

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).extension<AppColors>()!;
    final localization = AppLocalizations.of(context)!;

    return Column(
      children: [
        Container(
          margin: const EdgeInsetsDirectional.symmetric(horizontal: 16),
          height: 1,
          color: colors.strokeColor,
        ),
        Container(height: 20),
        Container(
          margin: const EdgeInsetsDirectional.symmetric(horizontal: 16),
          padding: const EdgeInsetsDirectional.all(16),
          decoration: BoxDecoration(
            color: colors.background,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: colors.strokeColor, width: 1),
          ),
          child: Column(
            children: [
              DropdownField(
                  label: localization.status, value: 'Backlog', isStatus: true),
              Container(height: 16),
              DropdownField(
                  label: localization.department,
                  value: 'Design',
                  isDepartment: true),
              Container(height: 16),
              AssigneeField(label: localization.assignee, showLabel: true),
              Container(height: 12),
              const AssigneeField(showLabel: false),
            ],
          ),
        ),
        Container(height: 24),
      ],
    );
  }
}

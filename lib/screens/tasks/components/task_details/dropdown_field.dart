import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';

class DropdownField extends StatelessWidget {
  final String label;
  final String value;
  final bool isStatus;
  final bool isDepartment;

  const DropdownField({
    super.key,
    required this.label,
    required this.value,
    this.isStatus = false,
    this.isDepartment = false,
  });

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).extension<AppColors>()!;
    final textStyles = Theme.of(context).extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: textStyles.body2.copyWith(
              color: colors.secondaryText,
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: Container(
            height: 40,
            padding: const EdgeInsetsDirectional.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              color: colors.backgroundContainer,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: colors.strokeColor, width: 1),
            ),
            child: Row(
              children: [
                Text(
                  value,
                  style: textStyles.body2.copyWith(
                    color: isStatus
                        ? colors.warning
                        : isDepartment
                            ? colors.secondaryText
                            : colors.secondaryText,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: colors.primary,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';

class CardHeader extends StatelessWidget {
  final String taskId;
  const CardHeader({super.key, this.taskId = '#UI007'});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
            decoration: BoxDecoration(
              color: colors.background,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              taskId,
              style: textStyles.body2.copyWith(
                color: colors.secondaryText,
              ),
            ),
          ),
          Row(
            children: [
              Icon(
                Icons.share_outlined,
                size: 20,
                color: colors.secondaryText,
              ),
              Container(margin: const EdgeInsets.only(left: 16)),
              Icon(
                Icons.edit_outlined,
                size: 20,
                color: colors.secondaryText,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

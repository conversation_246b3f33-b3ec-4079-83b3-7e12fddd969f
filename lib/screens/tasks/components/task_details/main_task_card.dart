import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'card_header.dart';
import 'task_title.dart';
import 'task_fields.dart';
import 'description_section.dart';
import 'attachment_section.dart';

class MainTaskCard extends StatelessWidget {
  const MainTaskCard({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CardHeader(),
          TaskTitle(title: 'Change email option process'),
          TaskFields(),
          DescriptionSection(
            description:
                'Design A User Interface For The [Interface Description] Page, Ensuring It Aligns With The User Experience And Reflects The System\'s Visual Identity. The Design Includes Organizing Content, Defining Interactions, And Ensuring Ease Of Use And Navigation Within The Interface.',
          ),
          AttachmentSection(),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';

class PopupHeader extends StatelessWidget {
  final VoidCallback? onBack;

  const PopupHeader({super.key, this.onBack});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsetsDirectional.all(16),
      child: Row(
        children: [
          GestureDetector(
            onTap: onBack ?? () => Navigator.of(context).pop(),
            child: Container(
              padding: const EdgeInsetsDirectional.all(8),
              decoration: BoxDecoration(
                color: colors.backgroundContainer,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: colors.strokeColor,
                  width: 1,
                ),
              ),
              child: Icon(
                DirectionHelpers.getBackArrowIcon(context),
                color: colors.primaryText,
                size: 20,
              ),
            ),
          ),
          Container(margin: const EdgeInsetsDirectional.only(start: 12)),
          Text(
            localization.taskDetails,
            style: textStyles.headline3.copyWith(
              color: colors.secondaryText,
            ),
          ),
        ],
      ),
    );
  }
}

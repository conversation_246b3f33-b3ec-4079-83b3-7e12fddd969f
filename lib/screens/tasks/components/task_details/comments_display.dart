import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';

/// Model class for representing a comment
class CommentModel {
  final String userName;
  final String userAvatar;
  final String commentText;
  final String timestamp;

  CommentModel({
    required this.userName,
    required this.userAvatar,
    required this.commentText,
    required this.timestamp,
  });
}

/// Widget component for displaying existing user comments
class CommentsDisplay extends StatelessWidget {
  const CommentsDisplay({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    // Sample comments data - this would typically come from API or state management
    final List<CommentModel> comments = [
      CommentModel(
        userName: '<PERSON><PERSON><PERSON>',
        userAvatar: 'assets/images/person.png', // Using person.png from assets
        commentText:
            'Design A User Interface For The [Interface Description] Page, Ensuring It Aligns With The User Experience And Reflects The System\'s Visual Identity. The Design Includes Organizing Content, Defining Interactions, And Ensuring Ease Of Use And Navigation Within The Interface.',
        timestamp: '8:00 AM',
      ),
      CommentModel(
        userName: 'Ethaar Hussein',
        userAvatar: 'assets/images/person.png', // Using person.png from assets
        commentText:
            'Design A User Interface For The [Interface Description] Page, Ensuring It Aligns With The User Experience And Reflects The System\'s Visual Identity. The Design Includes Organizing Content, Defining Interactions, And Ensuring Ease Of Use And Navigation Within The Interface.',
        timestamp: '8:00 AM',
      ),
      CommentModel(
        userName: 'Ethaar Hussein',
        userAvatar: 'assets/images/person.png', // Using person.png from assets
        commentText:
            'Design A User Interface For The [Interface Description] Page, Ensuring It Aligns With The User Experience And Reflects The System\'s Visual Identity. The Design Includes Organizing Content, Defining Interactions, And Ensuring Ease Of Use And Navigation Within The Interface.',
        timestamp: '8:00 AM',
      ),
    ];

    return Container(
      width: double.infinity,
      margin: const EdgeInsetsDirectional.fromSTEB(0, 16, 0, 0),
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: colors.strokeColor, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Comments header with count
          Container(
            margin: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
            child: Text(
              '${localization.comments} (${comments.length})',
              style: textStyles.body2.copyWith(
                color: colors.primaryText,
              ),
            ),
          ),

          // Comments list
          Container(
            margin: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 16),
            child: Column(
              children: comments
                  .map((comment) => _buildCommentItem(
                        context,
                        comment,
                        colors,
                        textStyles,
                      ))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// Build individual comment item widget
  Widget _buildCommentItem(
    BuildContext context,
    CommentModel comment,
    AppColors colors,
    TextStyles textStyles,
  ) {
    return Container(
      margin: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 16),
      padding: const EdgeInsetsDirectional.fromSTEB(12, 12, 12, 12),
      decoration: BoxDecoration(
        color: colors.background,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User avatar - now inside the comment container
          Container(
            width: 28,
            height: 28,
            margin: const EdgeInsetsDirectional.fromSTEB(0, 0, 12, 0),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: colors.strokeColor,
            ),
            child: ClipOval(
              child: Image.asset(
                'assets/images/person.png',
                width: 40,
                height: 40,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  // Fallback if image fails to load
                  return Container(
                    color: colors.primary.withOpacity(0.2),
                    child: Icon(
                      Icons.person,
                      color: colors.primary,
                      size: 24,
                    ),
                  );
                },
              ),
            ),
          ),

          // Comment content - user name, timestamp, and text
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User name and timestamp row
                Container(
                  margin: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // User name
                      Expanded(
                        child: Text(
                          comment.userName,
                          style: textStyles.headline4.copyWith(
                            color: colors.primaryText,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                      // Timestamp
                      Text(
                        comment.timestamp,
                        style: textStyles.body3.copyWith(
                          color: colors.tertiaryText,
                        ),
                      ),
                    ],
                  ),
                ),

                // Comment text
                Text(
                  comment.commentText,
                  style: textStyles.body2.copyWith(
                    color: colors.secondaryText,
                    height: 1.4, // Line height for better readability
                  ),
                  textAlign: TextAlign.start,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

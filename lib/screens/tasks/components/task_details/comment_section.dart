import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:carousel_slider/carousel_slider.dart';

class CommentSection extends StatefulWidget {
  const CommentSection({super.key});

  @override
  State<CommentSection> createState() => _CommentSectionState();
}

class _CommentSectionState extends State<CommentSection> {
  int _currentIndex = 0;

  // /// Function to handle comment selection
  // void _onCommentSelected(String comment) {
  //   // Show feedback to user about selected comment
  //   ScaffoldMessenger.of(context).showSnackBar(
  //     SnackBar(
  //       content: Text('Comment selected: $comment'),
  //       duration: const Duration(seconds: 2),
  //     ),
  //   );

  //   // Here you would typically send the comment to your backend
  //   print('Selected comment: $comment');
  // }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    // Predefined comment options with localization
    final List<String> commentOptions = [
      localization.canIGetMoreInfo,
      localization.statusUpdate,
      localization.taskCompleted,
      localization.needHelp,
      localization.approveTask,
    ];

    return Container(
      width: double.infinity,
      margin: const EdgeInsetsDirectional.fromSTEB(0, 16, 0, 0),
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: colors.strokeColor, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Container(
            margin: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 12),
            child: Text(
              localization.addComment,
              style: textStyles.body2.copyWith(
                color: colors.tertiaryText,
              ),
            ),
          ),

          // Comment options carousel
          Container(
            margin: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 16),
            child: CarouselSlider(
              options: CarouselOptions(
                height: 40, // Reduced height for more compact design
                viewportFraction: 0.48, // Show approximately 2 items at a time
                enableInfiniteScroll: false,
                padEnds: false,
                onPageChanged: (index, reason) {
                  setState(() {
                    _currentIndex = index;
                  });
                },
              ),
              items: commentOptions.asMap().entries.map((entry) {
                int index = entry.key;
                String comment = entry.value;
                bool isActive = index == _currentIndex;

                return Container(
                  margin: EdgeInsetsDirectional.only(
                    start: index == 0 ? 16 : 6, // Reduced margin for better fit
                    end: index == commentOptions.length - 1 ? 16 : 6,
                  ),
                  child: GestureDetector(
                    onTap: () {},
                    child: Container(
                      padding: const EdgeInsetsDirectional.symmetric(
                        horizontal: 12, // Reduced horizontal padding
                        vertical: 8, // Reduced vertical padding
                      ),
                      decoration: BoxDecoration(
                        color: colors.background,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          comment,
                          style: textStyles.body2.copyWith(
                            color: colors.secondaryText,
                            fontSize:
                                13, // Slightly smaller font size for better fit
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}

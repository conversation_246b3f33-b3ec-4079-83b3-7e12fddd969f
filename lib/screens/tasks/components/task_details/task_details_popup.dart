import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'popup_header.dart';
import 'main_task_card.dart';
import 'comment_section.dart';
import 'comments_display.dart';

class TaskDetailsPopup extends StatelessWidget {
  final VoidCallback? onBack;

  const TaskDetailsPopup({super.key, this.onBack});

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).extension<AppColors>()!;

    return Scaffold(
      backgroundColor: colors.background,
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            PopupHeader(onBack: onBack),
            const Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.fromLTRB(16, 16, 16, 0),
                child: <PERSON>umn(
                  children: [
                    // Main task card with dropdowns, description, attachment
                    MainTaskCard(),

                    // Comment section as separate container (has its own margin)
                    CommentSection(),

                    // Comments display section
                    CommentsDisplay(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

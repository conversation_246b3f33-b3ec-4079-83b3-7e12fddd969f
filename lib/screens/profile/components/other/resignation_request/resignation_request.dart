import 'package:ako_basma/styles/theme.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'dart:io';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';

class ResignationRequest extends StatefulWidget {
  const ResignationRequest({super.key, this.onBack});
  final VoidCallback? onBack;

  @override
  State<ResignationRequest> createState() => _ResignationRequestState();
}

class _ResignationRequestState extends State<ResignationRequest> {
  final TextEditingController titleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController amountController = TextEditingController();
  File? selectedFile;

  @override
  void dispose() {
    titleController.dispose();
    descriptionController.dispose();
    amountController.dispose();
    super.dispose();
  }

  // code for file picker
  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'pdf', 'png', 'jpeg'],
      );

      if (result != null &&
          result.files.isNotEmpty &&
          result.files.single.path != null) {
        setState(() {
          selectedFile = File(result.files.single.path!);
          // You can add code here to display the selected file name
        });
        print("File selected: ${result.files.single.path}");
      } else {
        print("No file selected or file path is null");
      }
    } catch (e) {
      print("Error picking file: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      decoration: BoxDecoration(
        color: colors.background,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          // Header with back button and title
          Container(
            padding: const EdgeInsetsDirectional.all(16),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () {
                    if (widget.onBack != null) {
                      widget.onBack!();
                    } else {
                      Navigator.of(context).pop();
                    }
                  },
                  child: Container(
                    padding: const EdgeInsetsDirectional.all(8),
                    decoration: BoxDecoration(
                      color: colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: colors.strokeColor,
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      DirectionHelpers.getBackArrowIcon(context),
                      color: colors.primaryText,
                      size: 20,
                    ),
                  ),
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(start: 12),
                ),
                Text(
                  localization.resignationRequest,
                  style: textStyles.headline4.copyWith(
                    color: colors.secondaryText,
                  ),
                ),
              ],
            ),
          ),
          // Main content - scrollable
          Expanded(
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Container(
                padding: const EdgeInsetsDirectional.symmetric(horizontal: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Container(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(10, 8, 10, 8),
                      child: Column(
                        children: [
                          // Reason for Resignation field
                          TextField(
                            controller: titleController,
                            maxLines: 6,
                            decoration: InputDecoration(
                              contentPadding:
                                  const EdgeInsetsDirectional.symmetric(
                                      horizontal: 16, vertical: 12),
                              labelText: 'Reason for Resignation',
                              floatingLabelBehavior: FloatingLabelBehavior.auto,
                              alignLabelWithHint: true,
                              labelStyle: textStyles.body2.copyWith(
                                color: colors.tertiaryText,
                              ),
                              filled: true,
                              fillColor: colors.backgroundContainer,
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.strokeColor,
                                  width: 1,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.primary,
                                  width: 1,
                                ),
                              ),
                            ),
                            style: textStyles.body2.copyWith(
                              color: colors.primaryText,
                            ),
                            textAlignVertical: TextAlignVertical.top,
                          ),

                          Container(
                            height: 10,
                          ),

                          // Additional Notes field
                          TextField(
                            controller: descriptionController,
                            maxLines: 6,
                            decoration: InputDecoration(
                              contentPadding:
                                  const EdgeInsetsDirectional.symmetric(
                                      horizontal: 16, vertical: 12),
                              labelText: 'Additional Notes',
                              floatingLabelBehavior: FloatingLabelBehavior.auto,
                              alignLabelWithHint: true,
                              labelStyle: textStyles.body2.copyWith(
                                color: colors.tertiaryText,
                              ),
                              filled: true,
                              fillColor: colors.backgroundContainer,
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.strokeColor,
                                  width: 1,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.primary,
                                  width: 1,
                                ),
                              ),
                            ),
                            style: textStyles.body2.copyWith(
                              color: colors.primaryText,
                            ),
                            textAlignVertical: TextAlignVertical.top,
                          ),

                          Container(
                            height: 10,
                          ),

                          // Last Working Day field
                          GestureDetector(
                            onTap: () async {
                              final DateTime? picked = await showDatePicker(
                                context: context,
                                initialDate: DateTime.now(),
                                firstDate: DateTime.now(),
                                lastDate: DateTime(2030),
                              );
                              if (picked != null) {
                                amountController.text =
                                    "${picked.day}/${picked.month}/${picked.year}";
                              }
                            },
                            child: AbsorbPointer(
                              child: TextField(
                                controller: amountController,
                                decoration: InputDecoration(
                                  contentPadding:
                                      const EdgeInsetsDirectional.symmetric(
                                          horizontal: 16, vertical: 12),
                                  labelText: 'Last Working Day',
                                  floatingLabelBehavior:
                                      FloatingLabelBehavior.auto,
                                  labelStyle: textStyles.body2.copyWith(
                                    color: colors.tertiaryText,
                                  ),
                                  filled: true,
                                  fillColor: colors.backgroundContainer,
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color: colors.strokeColor,
                                      width: 1,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color: colors.primary,
                                      width: 1,
                                    ),
                                  ),
                                ),
                                style: textStyles.body2.copyWith(
                                  color: colors.primaryText,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // file picker
                    Container(
                      margin:
                          const EdgeInsetsDirectional.fromSTEB(10, 10, 10, 16),
                      child: InkWell(
                        onTap: _pickFile,
                        child: DottedBorder(
                          color: colors.strokeColor,
                          strokeWidth: 1,
                          dashPattern: const [8, 2],
                          borderType: BorderType.RRect,
                          radius: const Radius.circular(8),
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsetsDirectional.symmetric(
                                vertical: 24),
                            decoration: BoxDecoration(
                              color: colors.backgroundContainer,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  width: 48,
                                  height: 48,
                                  decoration: BoxDecoration(
                                    color: colors.primaryVariant,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Material(
                                    color: Colors.transparent,
                                    child: Center(
                                      child: SvgPicture.asset(
                                        'assets/icons/workspace_screen/upload.svg',
                                        width: 24,
                                        height: 24,
                                      ),
                                    ),
                                  ),
                                ),
                                Container(
                                  height: 12,
                                ),
                                Text(
                                  localization.clickToUpload,
                                  style: textStyles.body2.copyWith(
                                    color: colors.primary,
                                  ),
                                ),
                                Container(
                                  height: 4,
                                ),
                                Text(
                                  localization.maxFileSize,
                                  style: textStyles.body2.copyWith(
                                    color: colors.tertiaryText,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // cancel and submit resignation buttons
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 32),
            child: Row(
              children: [
                // Cancel Button
                Expanded(
                  child: Container(
                    height: 48,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: colors.strokeColor,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(8),
                        onTap: () {
                          if (widget.onBack != null) {
                            widget.onBack!();
                          } else {
                            Navigator.of(context).pop();
                          }
                        },
                        child: Center(
                          child: Text(
                            localization.cancel,
                            style: textStyles.body.copyWith(
                              color: colors.secondaryText,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(start: 16),
                ),
                // Submit Resignation Button
                Expanded(
                  child: Container(
                    height: 48,
                    decoration: BoxDecoration(
                      color: colors.error,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(8),
                        onTap: () {
                          // Handle submit resignation
                          if (widget.onBack != null) {
                            widget.onBack!();
                          } else {
                            Navigator.of(context).pop();
                          }
                        },
                        child: Center(
                          child: Text(
                            'Submit Resignation',
                            style: textStyles.body.copyWith(
                              color: theme.colorScheme.onPrimary,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

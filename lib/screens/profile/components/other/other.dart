import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/profile/components/other/logout.dart';
import 'package:ako_basma/screens/profile/components/other/resignation_request/resignation_request.dart';
import 'package:ako_basma/screens/profile/components/settings/custom_settings_tile.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/util/ui/popups.dart';

class Other extends StatefulWidget {
  const Other({super.key});

  @override
  State<StatefulWidget> createState() {
    return _OtherState();
  }
}

class _OtherState extends State<Other> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsetsDirectional.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            localization.other,
            style: textStyles.headline4.copyWith(
              color: colors.secondaryText,
            ),
          ),
          // Resignation request button
          Container(
            margin: const EdgeInsetsDirectional.only(top: 8),
            child: GestureDetector(
              onTap: () {
                showAdaptivePopup(
                  context,
                  (ctx, sc) => ResignationRequest(
                    onBack: () => Navigator.pop(ctx),
                  ),
                  isDismissible: false,
                  scrollable: true,
                  contentPadding: EdgeInsets.zero,
                  topRadius: 0,
                  fullScreen: true,
                  useRootNavigator: true,
                );
              },
              child: CustomSettingsTile(
                title: localization.resignationRequest,
                hasToggle: false,
              ),
            ),
          ),

          // Logout button
          Container(
            margin: const EdgeInsetsDirectional.only(top: 8),
            child: GestureDetector(
              onTap: () {
                showDialog(
                  context: context,
                  builder: (BuildContext context) {
                    return const Logout();
                  },
                );
              },
              child: CustomSettingsTile(
                title: localization.logout,
                hasToggle: false,
                titleColor: colors.error,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

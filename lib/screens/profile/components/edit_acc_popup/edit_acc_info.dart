import 'package:ako_basma/screens/profile/components/edit_acc_popup/acc_info.dart';
import 'package:ako_basma/screens/profile/components/edit_acc_popup/not_editable.dart';
import 'package:ako_basma/screens/profile/components/edit_acc_popup/other_info.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';

class EditAccInfo extends StatefulWidget {
  const EditAccInfo({super.key});

  @override
  State<EditAccInfo> createState() => _EditAccInfoState();
}

class _EditAccInfoState extends State<EditAccInfo> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      decoration: BoxDecoration(
        color: colors.background,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          // Header with back button and title
          Container(
            padding: const EdgeInsetsDirectional.all(16),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    padding: const EdgeInsetsDirectional.all(8),
                    decoration: BoxDecoration(
                      color: colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: colors.strokeColor,
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      DirectionHelpers.getBackArrowIcon(context),
                      color: colors.primaryText,
                      size: 20,
                    ),
                  ),
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(start: 12),
                ),
                Text(
                  localization.editAccountInfo,
                  style: textStyles.headline4.copyWith(
                    color: colors.secondaryText,
                  ),
                ),
              ],
            ),
          ),

          // Main content - scrollable
          Expanded(
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                children: [
                  const AccInfo(),
                  const NotEditable(),
                  const OtherInfo(),
                ],
              ),
            ),
          ),

          // Save button
          Container(
            padding: const EdgeInsetsDirectional.symmetric(
                horizontal: 16, vertical: 16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  // Handle save action
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: colors.primary,
                  foregroundColor: Colors.white,
                  minimumSize: const Size.fromHeight(48),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  localization.save,
                  style: textStyles.body.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/styles/util.dart';
import 'package:ako_basma/screens/profile/components/performance&achievements/specific_expense_card_popup.dart';
import 'package:ako_basma/util/ui/popups.dart';

class ExpensesCard extends StatefulWidget {
  const ExpensesCard({super.key});

  @override
  State<ExpensesCard> createState() => _ExpensesCardState();
}

class _ExpensesCardState extends State<ExpensesCard> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;

    return GestureDetector(
      onTap: () {
        showAdaptivePopup(
          context,
          (ctx, sc) => SpecificExpenseCardPopup(
            onBack: () => Navigator.pop(ctx),
          ),
          isDismissible: false,
          scrollable: true,
          contentPadding: EdgeInsets.zero,
          topRadius: 0,
          fullScreen: true,
          useRootNavigator: true,
        );
      },
      child: Container(
        width: screenWidth - 16,
        margin: const EdgeInsetsDirectional.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: colors.backgroundContainer,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: colors.strokeColor,
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section with title
            Container(
              padding: const EdgeInsetsDirectional.fromSTEB(20, 20, 20, 0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    localization.expenseTitle,
                    style: textStyles.body.copyWith(
                      color: colors.primaryText,
                    ),
                  ),
                  Container(
                    margin: const EdgeInsetsDirectional.only(top: 10),
                  ),
                ],
              ),
            ),

            // Amount section - this touches the start edge but only wraps content
            Align(
              alignment: AlignmentDirectional.centerStart,
              child: Container(
                padding: const EdgeInsetsDirectional.symmetric(
                    horizontal: 20, vertical: 16),
                decoration: BoxDecoration(
                  color: colors.primaryVariant,
                  borderRadius: const BorderRadiusDirectional.only(
                    topEnd: Radius.circular(8),
                    bottomEnd: Radius.circular(8),
                  ),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.baseline,
                  textBaseline: TextBaseline.alphabetic,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'IQD',
                      style: textStyles.body2.copyWith(
                        color: colors.tertiaryText,
                        fontSize: 12,
                      ),
                    ),
                    Container(
                      margin: const EdgeInsetsDirectional.only(start: 8),
                    ),
                    Text(
                      localization.expensesAmount,
                      style: textStyles.headline2.copyWith(
                        color: colors.primary,
                        fontSize: 32,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Bottom section with date and status
            Container(
              padding: const EdgeInsetsDirectional.fromSTEB(20, 14, 20, 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    localization.expensesDate,
                    style: textStyles.body.copyWith(
                      color: colors.tertiaryText,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsetsDirectional.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: colors.successContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      localization.paid,
                      style: textStyles.body3.copyWith(
                        color: colors.success,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

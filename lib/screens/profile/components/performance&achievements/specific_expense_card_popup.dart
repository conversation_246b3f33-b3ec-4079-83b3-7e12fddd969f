import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'dart:io';
import 'package:solar_icons/solar_icons.dart';

class SpecificExpenseCardPopup extends StatefulWidget {
  const SpecificExpenseCardPopup({super.key, this.onBack});
  final VoidCallback? onBack;

  @override
  State<SpecificExpenseCardPopup> createState() =>
      _SpecificExpenseCardPopupState();
}

class _SpecificExpenseCardPopupState extends State<SpecificExpenseCardPopup> {
  final TextEditingController titleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController amountController = TextEditingController();
  File? selectedFile;
  @override
  void dispose() {
    titleController.dispose();
    descriptionController.dispose();
    amountController.dispose();
    super.dispose();
  }

  // code for file picker
  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'pdf', 'png', 'jpeg'],
      );

      if (result != null &&
          result.files.isNotEmpty &&
          result.files.single.path != null) {
        setState(() {
          selectedFile = File(result.files.single.path!);
          // You can add code here to display the selected file name
        });
        print("File selected: ${result.files.single.path}");
      } else {
        print("No file selected or file path is null");
      }
    } catch (e) {
      print("Error picking file: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Container(
      decoration: BoxDecoration(
        color: colors.background,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          // Header with back button and title
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () {
                    if (widget.onBack != null) {
                      widget.onBack!();
                    } else {
                      Navigator.of(context).pop();
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: colors.strokeColor,
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      SolarIconsOutline.altArrowLeft,
                      color: colors.primaryText,
                      size: 20,
                    ),
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(left: 12),
                ),
                Text(
                  Labels.expenseTitle,
                  style: textStyles.headline4.copyWith(
                    color: colors.secondaryText,
                  ),
                ),
              ],
            ),
          ),
          // Main content - scrollable
          Expanded(
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Container(
                      padding: const EdgeInsets.fromLTRB(10, 8, 10, 8),
                      child: Column(
                        children: [
                          // Waiting for manager approval status
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 10,
                            ),
                            decoration: BoxDecoration(
                              color: colors.warningContainer,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Container(
                                  margin: const EdgeInsets.only(left: 4),
                                ),
                                Text(
                                  Labels.waitingManagerApproval,
                                  style: textStyles.body2.copyWith(
                                    color: colors.warning,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          Container(
                            height: 16,
                          ),

                          // Title field
                          TextField(
                            controller: titleController,
                            decoration: InputDecoration(
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 12),
                              labelText: 'Title',
                              floatingLabelBehavior: FloatingLabelBehavior.auto,
                              labelStyle: textStyles.body2.copyWith(
                                color: colors.tertiaryText,
                              ),
                              filled: true,
                              fillColor: colors.backgroundContainer,
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.strokeColor,
                                  width: 1,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.primary,
                                  width: 1,
                                ),
                              ),
                            ),
                            style: textStyles.body2.copyWith(
                              color: colors.primaryText,
                            ),
                          ),

                          Container(
                            height: 10,
                          ),

                          // Description field
                          TextField(
                            controller: descriptionController,
                            maxLines: 3,
                            decoration: InputDecoration(
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 12),
                              labelText: 'Description',
                              floatingLabelBehavior: FloatingLabelBehavior.auto,
                              labelStyle: textStyles.body2.copyWith(
                                color: colors.tertiaryText,
                              ),
                              filled: true,
                              fillColor: colors.backgroundContainer,
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.strokeColor,
                                  width: 1,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.primary,
                                  width: 1,
                                ),
                              ),
                            ),
                            style: textStyles.body2.copyWith(
                              color: colors.primaryText,
                            ),
                          ),

                          Container(
                            height: 10,
                          ),

                          // Amount field
                          TextField(
                            controller: amountController,
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly
                            ],
                            decoration: InputDecoration(
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 12),
                              labelText: 'Amount IQD',
                              floatingLabelBehavior: FloatingLabelBehavior.auto,
                              labelStyle: textStyles.body2.copyWith(
                                color: colors.tertiaryText,
                              ),
                              filled: true,
                              fillColor: colors.backgroundContainer,
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.strokeColor,
                                  width: 1,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.primary,
                                  width: 1,
                                ),
                              ),
                            ),
                            style: textStyles.body2.copyWith(
                              color: colors.primaryText,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // file picker
                    Container(
                      margin: const EdgeInsets.fromLTRB(10, 10, 10, 16),
                      child: InkWell(
                        onTap: _pickFile,
                        child: DottedBorder(
                          color: colors.strokeColor,
                          strokeWidth: 1,
                          dashPattern: const [8, 2],
                          borderType: BorderType.RRect,
                          radius: const Radius.circular(8),
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(vertical: 24),
                            decoration: BoxDecoration(
                              color: colors.backgroundContainer,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  width: 48,
                                  height: 48,
                                  decoration: BoxDecoration(
                                    color: colors.primaryVariant,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Material(
                                    color: Colors.transparent,
                                    child: Center(
                                      child: SvgPicture.asset(
                                        'assets/icons/workspace_screen/upload.svg',
                                        width: 24,
                                        height: 24,
                                      ),
                                    ),
                                  ),
                                ),
                                Container(
                                  height: 12,
                                ),
                                Text(
                                  Labels.clickToUpload,
                                  style: textStyles.body2.copyWith(
                                    color: colors.primary,
                                  ),
                                ),
                                Container(
                                  height: 4,
                                ),
                                Text(
                                  Labels.maxFileSize,
                                  style: textStyles.body2.copyWith(
                                    color: colors.tertiaryText,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

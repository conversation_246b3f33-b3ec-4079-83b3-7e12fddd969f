import 'package:ako_basma/screens/profile/components/settings/HR%20chat%20screen/hr_approval_message.dart';
import 'package:ako_basma/screens/profile/components/settings/HR%20chat%20screen/hr_messages_list.dart';
import 'package:ako_basma/screens/profile/components/settings/HR%20chat%20screen/hr_chat_input_field.dart';
import 'package:ako_basma/screens/profile/components/settings/HR%20chat%20screen/hr_chat_header.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';

/// HR Chat Screen
class HrChatScreen extends StatefulWidget {
  const HrChatScreen({super.key});

  @override
  State<HrChatScreen> createState() => _HrChatScreenState();
}

class _HrChatScreenState extends State<HrChatScreen> {
  /// Handles sending message to HR
  void _handleSendMessage(String message) {
    // TODO: Implement HR message sending logic here
    // For now, just print the message
    print('Message sent to HR: $message');
  }

  /// Handles attachment functionality
  void _handleAttachmentPress() {
    // TODO: Implement attachment functionality for HR chat
    print('Attachment pressed in HR chat');
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    return Scaffold(
      backgroundColor: colors.background,
      body: Column(
        children: [
          // HR Chat Header - matches standard chat screen layout
          HrChatHeader(
            onBackPressed: () => Navigator.pop(context),
          ),
          const Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // HR Approval message
                  HrApprovalMessage(),

                  // Chat messages list
                  HrMessagesList(),
                ],
              ),
            ),
          ),
          // HR Chat input field component
          HrChatInputField(
            onSendMessage: _handleSendMessage,
            onAttachmentPressed: _handleAttachmentPress,
          ),
        ],
      ),
    );
  }
}

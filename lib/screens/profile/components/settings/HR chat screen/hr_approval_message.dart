import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/labels.dart';

class HrApprovalMessage extends StatelessWidget {
  const HrApprovalMessage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      width: screenWidth,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: colors.successContainer,
      ),
      child: Row(
        children: [
          // Approval message text
          Expanded(
            child: Text(
              Labels.hrApprovalMessage,
              style: textStyles.body3.copyWith(
                color: colors.success,
              ),
              textAlign: TextAlign.start,
            ),
          ),
        ],
      ),
    );
  }
}

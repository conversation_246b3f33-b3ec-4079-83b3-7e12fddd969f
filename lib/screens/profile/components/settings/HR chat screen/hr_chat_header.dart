import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/labels.dart';
import 'package:solar_icons/solar_icons.dart';

/// HR Chat header component
/// Displays back arrow, HR profile picture, HR name and last seen status
class HrChatHeader extends StatelessWidget {
  final VoidCallback onBackPressed;

  const HrChatHeader({
    super.key,
    required this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      width: screenWidth,
      color: colors.backgroundContainer,
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: MediaQuery.of(context).padding.top + 8, // Blend with status bar
        bottom: 12,
      ),
      child: Row(
        children: [
          // Back button
          Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: onBackPressed,
              child: Container(
                padding: const EdgeInsets.all(8),
                child: Icon(
                  SolarIconsOutline.altArrowLeft,
                  size: 24,
                  color: colors.primaryText,
                ),
              ),
            ),
          ),

          // HR Profile section
          Expanded(
            child: Container(
              margin: const EdgeInsets.only(left: 4),
              child: Row(
                children: [
                  // HR Profile picture
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: colors.primaryVariant,
                    ),
                    child: ClipOval(
                      child: Image.asset(
                        'assets/images/person.png',
                        width: 40,
                        height: 40,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          // Fallback to icon if image fails to load
                          return Container(
                            color: colors.primary.withOpacity(0.1),
                            child: Icon(
                              Icons.person,
                              size: 24,
                              color: colors.primary,
                            ),
                          );
                        },
                      ),
                    ),
                  ),

                  // HR Name and Last Seen
                  Expanded(
                    child: Container(
                      margin: const EdgeInsets.only(left: 12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // HR Name
                          Text(
                            Labels.hrName,
                            style: textStyles.headline4.copyWith(
                              color: colors.primaryText,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),

                          // Last Seen
                          Container(
                            margin: const EdgeInsets.only(top: 2),
                            child: Text(
                              Labels.hrLastSeen,
                              style: textStyles.body3.copyWith(
                                color: colors.tertiaryText,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

class ChatWithHR extends StatefulWidget {
  const ChatWithHR({super.key});

  @override
  State<ChatWithHR> createState() => _ChatWithHRState();
}

class _ChatWithHRState extends State<ChatWithHR> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: colors.backgroundContainer,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Logout Icon
            Container(
              width: 100,
              height: 70,
              decoration: BoxDecoration(
                color: colors.primaryVariant,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: SvgPicture.asset(
                  'assets/icons/profile_screen/chat_with_hr.svg',
                  width: 35,
                  height: 35,
                ),
              ),
            ),
            Container(
              margin: const EdgeInsets.only(top: 24),
            ),
            // Title
            Text(
              Labels.requestToChatWithHR,
              style: textStyles.body.copyWith(
                color: colors.primaryText,
              ),
            ),
            Container(
              margin: const EdgeInsets.only(top: 16),
            ),
            // Confirmation Message
            Text(
              'Are You Sure You Want to Send the Request?',
              style: textStyles.body2.copyWith(
                color: colors.secondaryText,
              ),
              textAlign: TextAlign.center,
            ),
            Container(
              margin: const EdgeInsets.only(top: 20),
            ),
            // Buttons Row
            Row(
              children: [
                // Cancel Button
                Expanded(
                  flex: 1,
                  child: Container(
                    height: 48,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: colors.strokeColor,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(8),
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: Center(
                          child: Text(
                            Labels.cancel,
                            style: textStyles.body.copyWith(
                              color: colors.secondaryText,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(left: 16),
                ),
                // Send Button
                Expanded(
                  flex: 2,
                  child: Container(
                    height: 48,
                    decoration: BoxDecoration(
                      color: colors.primary,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(8),
                        onTap: () {
                          // Close the dialog first
                          Navigator.of(context).pop();
                          // Navigate to HR chat screen
                          context.push('/hr-chat');
                        },
                        child: Center(
                          child: Text(
                            Labels.send,
                            style: textStyles.body.copyWith(
                              color: theme.colorScheme.onPrimary,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

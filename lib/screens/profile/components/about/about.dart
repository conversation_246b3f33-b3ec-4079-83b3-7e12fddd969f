import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/profile/components/settings/custom_settings_tile.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

class About extends StatefulWidget {
  const About({super.key});

  @override
  State<StatefulWidget> createState() {
    return _AboutState();
  }
}

class _AboutState extends State<About> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            Labels.about,
            style: textStyles.headline4.copyWith(
              color: colors.secondaryText,
            ),
          ),
          Container(
            margin: const EdgeInsets.only(top: 8),
            child: Column(
              children: [
                CustomSettingsTile(
                  title: Labels.aboutUs,
                  hasToggle: false,
                  onTap: () {
                    // Handle privacy policy tap
                    print('About Us tapped');
                  },
                ),
                Container(
                  margin: const EdgeInsets.only(top: 8),
                ),
                CustomSettingsTile(
                  title: Labels.termsOfUse,
                  hasToggle: false,
                  onTap: () {
                    // Handle terms of service tap
                    print('Terms of Use tapped');
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

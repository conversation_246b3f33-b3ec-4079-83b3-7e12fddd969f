import 'package:ako_basma/screens/profile/components/account_info/account_info.dart';
import 'package:ako_basma/screens/profile/components/feedback/feedback.dart'
    as manager_feedback;
import 'package:ako_basma/screens/profile/components/performance&achievements/performance.dart';
import 'package:ako_basma/screens/profile/components/settings/settings.dart';
import 'package:ako_basma/screens/profile/components/about/about.dart';
import 'package:ako_basma/screens/profile/components/other/other.dart';
import 'package:flutter/material.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsetsDirectional.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  child: accountInfo(context),
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(top: 8),
                  child: const Performance(),
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(top: 8),
                  child: const manager_feedback.Feedback(),
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(top: 8),
                  child: const Settings(),
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(top: 8),
                  child: const About(),
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(top: 8),
                  child: const Other(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

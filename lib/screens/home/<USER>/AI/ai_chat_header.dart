import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/labels.dart';
import 'package:solar_icons/solar_icons.dart';

/// AI Chat header component
/// Displays back arrow and "Chat with AI" title only
class AIChatHeader extends StatelessWidget {
  final VoidCallback onBackPressed;

  const AIChatHeader({
    super.key,
    required this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      width: screenWidth,
      color: colors.backgroundContainer,
      padding: EdgeInsets.only(
        left: 20,
        right: 16,
        top: MediaQuery.of(context).padding.top + 8, // Blend with status bar
        bottom: 12,
      ),
      child: Row(
        children: [
          // Back button
          Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: onBackPressed,
              child: Container(
                padding: const EdgeInsets.all(8),
                child: Icon(
                  Directionality.of(context) == TextDirection.ltr
                      ? SolarIconsOutline.altArrowLeft
                      : SolarIconsOutline.altArrowRight,
                  size: 22,
                  color: colors.primaryText,
                ),
              ),
            ),
          ),

          // Chat with AI title
          Expanded(
            child: Container(
              margin: const EdgeInsets.only(left: 12),
              child: Text(
                Labels.aiChat,
                style: textStyles.headline3.copyWith(
                  color: colors.secondaryText,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

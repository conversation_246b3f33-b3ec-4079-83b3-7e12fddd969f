import 'package:ako_basma/components/gradient_mask/gradient_mask.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/labels.dart';

/// AI Greeting component with logo and gradient text

class AIGreeting extends StatelessWidget {
  const AIGreeting({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      width: screenWidth,
      padding:
          const EdgeInsetsDirectional.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Ako <PERSON>sma Logo
          Container(
            margin: const EdgeInsets.only(bottom: 24),
            child: Image.asset(
              'assets/images/ako_basma_logo.png',
              width: 44,
              height: 44,
              fit: BoxFit.contain,
              // errorBuilder: (context, error, stackTrace) {
              //   // Fallback if logo fails to load
              //   return Container(
              //     width: 80,
              //     height: 80,
              //     decoration: BoxDecoration(
              //       shape: BoxShape.circle,
              //       gradient: colors.primaryGradient,
              //     ),
              //     child: Center(
              //       child: Text(
              //         'AI',
              //         style: textStyles.headline.copyWith(
              //           color: Colors.white,
              //           fontWeight: FontWeight.bold,
              //         ),
              //       ),
              //     ),
              //   );
              // },
            ),
          ),

          // Gradient greeting text
          // Gradient text using Text widget with hardcoded blue to red gradient
          GradientMask(
            gradient: const LinearGradient(
              colors: [
                Color(0xFF0090BC), // Blue
                Color(0xFFD45969),
                Color(0xFF65C9E8), // Red
              ],
            ),
            child: Text(
              Labels.aiGreeting,
              style: textStyles.body2.copyWith(),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}

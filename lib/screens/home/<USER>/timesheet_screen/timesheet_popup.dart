import 'package:ako_basma/screens/home/<USER>/timesheet_screen/components/timesheet_shift_details.dart';
import 'package:ako_basma/screens/home/<USER>/timesheet_screen/components/custom_date_pay_card.dart';
import 'package:ako_basma/screens/home/<USER>/timesheet_screen/components/filter_dialog_box.dart';
import 'package:ako_basma/screens/home/<USER>/timesheet_screen/components/timesheet_card.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';

class TimesheetPopup extends StatefulWidget {
  const TimesheetPopup({super.key});

  @override
  State<TimesheetPopup> createState() => _TimesheetPopupState();
}

class _TimesheetPopupState extends State<TimesheetPopup> {
  String filter = '';
  String filterLabel = '';
  DateTime? fromDate;
  DateTime? toDate;

  String _monthName(DateTime date) {
    // Use DateFormat if intl is imported, otherwise use a static list
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[date.month - 1];
  }

  // date format helper
  String _formatDateRange(DateTime start, DateTime end) {
    if (start.year == end.year &&
        start.month == end.month &&
        start.day == end.day) {
      return "${start.day.toString().padLeft(2, '0')} ${_monthName(start)} ${start.year}";
    } else {
      return "${start.day.toString().padLeft(2, '0')} ${_monthName(start)} - ${end.day.toString().padLeft(2, '0')} ${_monthName(end)} ${end.year}";
    }
  }

  @override
  void initState() {
    super.initState();
    final localization = AppLocalizations.of(context)!;
    filter = localization.filter;
    filterLabel = localization.filter;
    fromDate = null;
    toDate = null;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header with back button and title
            Container(
              padding: const EdgeInsetsDirectional.all(16.0),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsetsDirectional.all(8),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        DirectionHelpers.getBackArrowIcon(context),
                        color: colors.primaryText,
                        size: 20,
                      ),
                    ),
                  ),
                  Container(
                      margin: const EdgeInsetsDirectional.only(start: 16)),
                  Text(
                    localization.timeSheet,
                    style: textStyles.headline4.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ],
              ),
            ),

            // Filter row
            Container(
              padding: const EdgeInsetsDirectional.symmetric(horizontal: 16),
              child: Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return FilterDialogBox(
                              onSave: (filter, fromDate, toDate) {
                                setState(() {
                                  if (filter == 'Custom Date Range') {
                                    this.filter = localization.customDateRange;
                                  } else if (filter == 'Today') {
                                    this.filter = localization.today;
                                  } else {
                                    this.filter = localization.filter;
                                  }
                                  this.fromDate = fromDate;
                                  this.toDate = toDate;
                                });
                              },
                              onCancel: () {
                                // Handle cancel
                              },
                            );
                          },
                        );
                      },
                      child: Container(
                        height: 40,
                        decoration: BoxDecoration(
                          border: Border.all(color: colors.strokeColor),
                          borderRadius: BorderRadius.circular(8),
                          color: colors.backgroundContainer,
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsetsDirectional.symmetric(
                                  horizontal: 12),
                              child: Text(
                                filter,
                                style: textStyles.body2.copyWith(
                                  color: colors.secondaryText,
                                ),
                              ),
                            ),
                            const Spacer(),
                            Container(
                              padding:
                                  const EdgeInsetsDirectional.only(end: 12),
                              child: Icon(
                                Icons.keyboard_arrow_down,
                                color: colors.secondaryText,
                                size: 20,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Content area
            Expanded(
              child: Container(
                padding: const EdgeInsetsDirectional.symmetric(horizontal: 16),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Container(height: 16),

                      // Show different content based on filter
                      if ((filter == localization.customDateRange ||
                              filter == localization.today) &&
                          filter != localization.filter)
                        ...[]
                      else ...[
                        // Default timesheet entries when no specific filter
                        TimesheetCard(
                          title: "8",
                          titleTextStyle: textStyles.headline3.copyWith(
                            color: colors.primary,
                          ),
                          date: "12",
                          month: "Dec",
                          year: "2024",
                          location: localization.location,
                          status: localization.taskCompleted,
                          statusColor: colors.success,
                          statusBgColor: colors.successContainer,
                        ),
                        TimesheetCard(
                          title: "6",
                          titleTextStyle: textStyles.headline3.copyWith(
                            color: colors.primary,
                          ),
                          date: "11",
                          month: "Dec",
                          year: "2024",
                          location: localization.location,
                          status: localization.pending,
                          statusColor: colors.warning,
                          statusBgColor: colors.warningContainer,
                        ),
                        TimesheetCard(
                          title: "8",
                          titleTextStyle: textStyles.headline3.copyWith(
                            color: colors.primary,
                          ),
                          date: "10",
                          month: "Dec",
                          year: "2024",
                          location: localization.location,
                          status: localization.taskCompleted,
                          statusColor: colors.success,
                          statusBgColor: colors.successContainer,
                        ),
                        TimesheetCard(
                          title: "8",
                          titleTextStyle: textStyles.headline3.copyWith(
                            color: colors.primary,
                          ),
                          date: "09",
                          month: "Dec",
                          year: "2024",
                          location: localization.location,
                          status: localization.taskCompleted,
                          statusColor: colors.success,
                          statusBgColor: colors.successContainer,
                        ),
                        TimesheetCard(
                          title: "8",
                          titleTextStyle: textStyles.headline3.copyWith(
                            color: colors.primary,
                          ),
                          date: "08",
                          month: "Dec",
                          year: "2024",
                          location: localization.location,
                          status: localization.taskCompleted,
                          statusColor: colors.success,
                          statusBgColor: colors.successContainer,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

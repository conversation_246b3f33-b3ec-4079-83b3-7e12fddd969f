import 'dart:typed_data';
import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/home/<USER>/shift_details/time_entry_card.dart';
import 'package:ako_basma/screens/home/<USER>/shift_details/top_snackbar.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/components/button/primary_button.dart';
import 'package:remixicon/remixicon.dart';
import 'package:ako_basma/screens/home/<USER>/shift_details/signature_popup.dart';
import 'package:ako_basma/screens/home/<USER>/shift_details/note_popup.dart';

class TimesheetShiftDetails extends StatefulWidget {
  final VoidCallback? onBack;
  final VoidCallback? resetToClockIn;

  const TimesheetShiftDetails({super.key, this.onBack, this.resetToClockIn});

  @override
  State<TimesheetShiftDetails> createState() => _TimesheetShiftDetailsState();
}

class _TimesheetShiftDetailsState extends State<TimesheetShiftDetails> {
  // state variables for time entry card which will be changed later
  String _clockInTime = '8:00 AM';
  String _clockOutTime = '3:30 PM';
  String _breakTime = '2:30 PM - 3:00';
  Uint8List? _signatureData;
  bool _hasSignature = false;

  // draw sign pop-up screen
  void _showSignaturePopup() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SignaturePopup(
        onCancel: () => Navigator.pop(context),
        onSave: (data) {
          setState(() {
            _signatureData = data;
            _hasSignature = true;
          });
          Navigator.pop(context);
        },
      ),
    );
  }

  // add note pop-up screen
  void _showNotePopup() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: NotePopup(
          onCancel: () => Navigator.pop(context),
          onSave: () => Navigator.pop(context),
        ),
      ),
    );
  }

  // top snackbar from top
  void _showTopSnackbar(BuildContext context) {
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => TopSnackbarWidget(
        message: 'Request sent successfully',
        onDismiss: () => overlayEntry.remove(),
      ),
    );

    overlay.insert(overlayEntry);

    // Auto remove after 3 seconds
    Future.delayed(const Duration(seconds: 3), () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;

    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Container(
          width: screenWidth,
          constraints: BoxConstraints(
            minHeight: mediaQuery.size.height,
          ),
          decoration: BoxDecoration(
            color: colors.background,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(12),
              topRight: Radius.circular(12),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              // Header with back button and title
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        if (widget.onBack != null) {
                          widget.onBack!();
                        } else {
                          Navigator.of(context).pop();
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: colors.backgroundContainer,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: colors.strokeColor,
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          Icons.arrow_back_ios_new,
                          color: colors.primaryText,
                          size: 20,
                        ),
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.only(left: 12),
                    ),
                    Text(
                      Labels.shiftDetails,
                      style: textStyles.headline4.copyWith(
                        color: colors.primaryText,
                      ),
                    ),
                  ],
                ),
              ),

              // Main content - scrollable
              Expanded(
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Container(
                          margin: const EdgeInsets.only(top: 8),
                        ),

                        // Two new containers when time is edited
                        Row(
                          children: [
                            // Draw Signature Container
                            Expanded(
                              child: GestureDetector(
                                onTap: _showSignaturePopup,
                                child: Container(
                                  height: 72,
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: colors.backgroundContainer,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: _hasSignature
                                          ? colors.primary
                                          : colors.primaryVariant,
                                      width: _hasSignature ? 2 : 1,
                                    ),
                                  ),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Stack(
                                        alignment: Alignment.center,
                                        children: [
                                          Icon(
                                            Remix.pencil_line,
                                            color: colors.primary,
                                            size: 20,
                                          ),
                                          if (_hasSignature)
                                            Positioned(
                                              right: -5,
                                              top: -5,
                                              child: Container(
                                                width: 12,
                                                height: 12,
                                                decoration: BoxDecoration(
                                                  color: colors.primary,
                                                  shape: BoxShape.circle,
                                                ),
                                                child: Icon(
                                                  Icons.check,
                                                  color: colors.primaryText,
                                                  size: 8,
                                                ),
                                              ),
                                            ),
                                        ],
                                      ),
                                      Container(
                                          margin:
                                              const EdgeInsets.only(top: 8)),
                                      Text(
                                        Labels.drawSignature,
                                        style: textStyles.body3.copyWith(
                                          color: colors.primary,
                                          fontWeight: _hasSignature
                                              ? FontWeight.w600
                                              : FontWeight.w400,
                                          fontSize: 12,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            Container(margin: const EdgeInsets.only(left: 8)),

                            // Add Note Container
                            Expanded(
                              child: GestureDetector(
                                onTap: _showNotePopup,
                                child: Container(
                                  height: 72,
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: colors.backgroundContainer,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                        width: 1, color: colors.primaryVariant),
                                  ),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Remix.file_text_line,
                                        color: colors.primary,
                                        size: 20,
                                      ),
                                      Container(
                                          margin:
                                              const EdgeInsets.only(top: 8)),
                                      Text(
                                        Labels.addNote,
                                        style: textStyles.body3.copyWith(
                                          color: colors.primary,
                                          fontWeight: FontWeight.w400,
                                          fontSize: 12,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),

                        Container(
                          margin: const EdgeInsets.only(top: 12),
                        ),
                        // Clock Out Container
                        Container(
                          width: MediaQuery.of(context).size.width - 32,
                          decoration: BoxDecoration(
                            color: colors.backgroundContainer,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Top section with transparent grey background
                              Container(
                                padding: const EdgeInsets.all(16),
                                width: double.infinity,
                                child: Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Expanded(
                                          child: Text(
                                            Labels.totalHours,
                                            style: textStyles.body3.copyWith(
                                              color: colors.secondaryText,
                                              fontSize: 12,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      ],
                                    ),
                                    Container(
                                      margin: const EdgeInsets.only(top: 8),
                                    ),
                                    Text(
                                      '10:30:00',
                                      style: textStyles.headline.copyWith(
                                        color: colors.primaryText,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 32,
                                      ),
                                    ),
                                    Container(
                                      margin: const EdgeInsets.only(top: 4),
                                    ),
                                    Text(
                                      'Saturday, 15 March 2025',
                                      style: textStyles.body2.copyWith(
                                        color: colors.secondaryText,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // Divider line
                              Container(
                                height: 0.1,
                              ),

                              // Bottom section
                              Container(
                                padding: const EdgeInsets.all(12),
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: colors.backgroundContainer,
                                  borderRadius: const BorderRadius.only(
                                    bottomLeft: Radius.circular(13),
                                    bottomRight: Radius.circular(13),
                                  ),
                                ),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: colors.primaryVariant,
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        Icons.location_on_outlined,
                                        color: colors.primary,
                                        size: 14,
                                      ),
                                    ),
                                    Container(
                                      margin: const EdgeInsets.only(left: 8),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: Text(
                                        Labels.location,
                                        style: textStyles.body3.copyWith(
                                          color: colors.secondaryText,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 1,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        Container(
                          margin: const EdgeInsets.only(top: 12),
                        ),

                        // Overtime Hours
                        Container(
                          width: MediaQuery.of(context).size.width - 32,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: colors.primaryVariant,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              width: 1,
                              color: colors.primaryVariant,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Remix.time_line,
                                    color: colors.primary,
                                    size: 20,
                                  ),
                                  Container(
                                    margin: const EdgeInsets.only(left: 8),
                                  ),
                                  Text(
                                    Labels.overTime,
                                    style: textStyles.body2.copyWith(
                                      color: colors.primary,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                              Container(
                                margin: const EdgeInsets.only(top: 8, left: 2),
                                child: Text(
                                  '12 Hours for Today',
                                  style: textStyles.headline.copyWith(
                                    color: colors.primaryText,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        Container(
                          margin: const EdgeInsets.only(top: 12),
                        ),

                        // Clock In
                        TimeEntryCard(
                          title: Labels.clockIn,
                          time: _clockInTime,
                          location: Labels.location,
                          onTimeChanged: (newTime) {
                            setState(() {
                              _clockInTime = newTime;
                            });
                          },
                        ),

                        Container(
                          margin: const EdgeInsets.only(top: 2),
                        ),

                        // Clock Out
                        TimeEntryCard(
                          title: Labels.clockOut,
                          time: _clockOutTime,
                          location: Labels.location,
                          onTimeChanged: (newTime) {
                            setState(() {
                              _clockOutTime = newTime;
                            });
                          },
                        ),

                        Container(
                          margin: const EdgeInsets.only(top: 2),
                        ),

                        // Break Time
                        TimeEntryCard(
                          title: Labels.breakTime,
                          time: _breakTime,
                          location: Labels.location,
                          onTimeChanged: (newTime) {
                            setState(() {
                              _breakTime = newTime;
                            });
                          },
                        ),

                        Container(
                          margin: const EdgeInsets.only(top: 2),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: colors.backgroundContainer,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                ),
                child: PrimaryButton(
                  label: Labels.sendShiftChangeRequest,
                  onTap: () {
                    // show top snackbar
                    _showTopSnackbar(context);

                    // Close the current popup
                    Navigator.of(context).pop();

                    // Reset to clock in screen if callback is provided
                    if (widget.resetToClockIn != null) {
                      widget.resetToClockIn!();
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

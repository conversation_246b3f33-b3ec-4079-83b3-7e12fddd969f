import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CustomDatePayCard extends StatelessWidget {
  const CustomDatePayCard({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Container(
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colors.strokeColor,
          width: 1,
        ),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: colors.primaryVariant,
                        shape: BoxShape.circle,
                      ),
                      child: SvgPicture.asset(
                        'assets/icons/workspace_screen/money.svg',
                        width: 11,
                        height: 11,
                        colorFilter: ColorFilter.mode(
                          colors.primary,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.only(left: 12),
                      child: Text(
                        Labels.payForThisPeriod,
                        style: textStyles.body2.copyWith(
                          color: colors.secondaryText,
                        ),
                      ),
                    ),
                  ],
                ),
                Text(
                  'IQD 100,000',
                  style: textStyles.headline4.copyWith(
                    color: colors.primary,
                  ),
                ),
              ],
            ),
            Container(
              padding: const EdgeInsets.only(top: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: colors.primaryVariant,
                          shape: BoxShape.circle,
                        ),
                        child: SvgPicture.asset(
                          'assets/icons/workspace_screen/clock_circle.svg',
                          width: 14,
                          height: 14,
                          colorFilter: ColorFilter.mode(
                            colors.primary,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.only(left: 14),
                        child: Text(
                          Labels.totalHours,
                          style: textStyles.body2.copyWith(
                            color: colors.secondaryText,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.only(left: 14),
                    child: Text(
                      '100',
                      style: textStyles.body2.copyWith(
                        color: colors.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

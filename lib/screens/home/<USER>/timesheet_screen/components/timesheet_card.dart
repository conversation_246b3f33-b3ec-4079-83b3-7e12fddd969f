import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/home/<USER>/timesheet_screen/components/timesheet_shift_details.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

class TimesheetCard extends StatelessWidget {
  final String location;
  final String title;
  final TextStyle titleTextStyle;
  final String date;
  final String month;
  final String year;
  final String status;
  final Color statusColor;
  final Color statusBgColor;
  final bool showStatus;

  const TimesheetCard({
    super.key,
    required this.location,
    required this.title,
    required this.titleTextStyle,
    required this.date,
    required this.month,
    required this.year,
    required this.status,
    required this.statusColor,
    required this.statusBgColor,
    this.showStatus = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const TimesheetShiftDetails(),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(top: 12),
        decoration: BoxDecoration(
          color: colors.backgroundContainer,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: colors.strokeColor,
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Time display at the top with black background
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: colors.background,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: titleTextStyle,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    Labels.totalShiftTime,
                    style: textStyles.body3.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ],
              ),
            ),

            // Divider
            Container(
              color: colors.background,
            ),

            // Content area
            Container(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Location with icon
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: colors.primaryVariant,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.location_on_outlined,
                          color: colors.primary,
                          size: 14,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          Labels.location,
                          style: textStyles.body3.copyWith(
                            color: colors.secondaryText,
                          ),
                        ),
                      ),
                    ],
                  ),

                  Container(
                    margin: const EdgeInsets.only(top: 16),
                  ),

                  // Date and status row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Date container with background
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: colors.primaryVariant,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Text(
                              date,
                              style: textStyles.headline.copyWith(
                                color: colors.primary,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              "$month $year",
                              style: textStyles.body3.copyWith(
                                color: colors.primary,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Status container (only if showStatus is true)
                      if (showStatus)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 8),
                          decoration: BoxDecoration(
                            color: statusBgColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            status,
                            style: textStyles.body2.copyWith(
                              color: statusColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:ako_basma/screens/home/<USER>/components/salary_detail_popup.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';

class SalaryPopup extends StatefulWidget {
  final VoidCallback? onBack;

  const SalaryPopup({
    super.key,
    this.onBack,
  });

  @override
  State<SalaryPopup> createState() => _SalaryPopupState();
}

class _SalaryPopupState extends State<SalaryPopup> {
  String selectedMonth = 'Month';
  String selectedYear = 'Year';

  // Sample data for salary entries
  final List<Map<String, dynamic>> salaryEntries = [
    {'date': '01/12/2024', 'amount': 'IQD 100,000,000', 'status': 'Paid'},
    {'date': '01/12/2024', 'amount': 'IQD 100,000,000', 'status': 'Paid'},
    {'date': '01/12/2024', 'amount': 'IQD 100,000,000', 'status': 'Paid'},
    {'date': '01/12/2024', 'amount': 'IQD 100,000,000', 'status': 'Paid'},
    {'date': '01/12/2024', 'amount': 'IQD 100,000,000', 'status': 'Paid'},
    {'date': '2024/12/1', 'amount': 'IQD 100,000,000', 'status': 'Paid'},
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header with back button and title
            Container(
              padding: const EdgeInsetsDirectional.all(16.0),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: widget.onBack ?? () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsetsDirectional.all(8),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        DirectionHelpers.getBackArrowIcon(context),
                        color: colors.primaryText,
                        size: 20,
                      ),
                    ),
                  ),
                  Container(
                      margin: const EdgeInsetsDirectional.only(start: 16)),
                  Text(
                    localization.salary,
                    style: textStyles.headline4.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ],
              ),
            ),

            Container(
              padding: const EdgeInsetsDirectional.symmetric(horizontal: 16.0),
              child: Row(
                children: [
                  // Month dropdown
                  Expanded(
                    child: Container(
                      margin: const EdgeInsetsDirectional.only(end: 8),
                      padding:
                          const EdgeInsetsDirectional.symmetric(horizontal: 16),
                      height: 48,
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: selectedMonth,
                          icon: Icon(Icons.keyboard_arrow_down,
                              color: colors.primary),
                          isExpanded: true,
                          dropdownColor: colors.backgroundContainer,
                          style: textStyles.body2.copyWith(
                            color: colors.secondaryText,
                          ),
                          onChanged: (String? newValue) {
                            setState(() {
                              selectedMonth = newValue!;
                            });
                          },
                          items: <String>[
                            'Month',
                            'January',
                            'February',
                            'March',
                            'April',
                            'May',
                            'June',
                            'July',
                            'August',
                            'September',
                            'October',
                            'November',
                            'December'
                          ].map<DropdownMenuItem<String>>((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(value),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ),

                  // Year dropdown
                  Expanded(
                    child: Container(
                      margin: const EdgeInsetsDirectional.only(start: 8),
                      padding:
                          const EdgeInsetsDirectional.symmetric(horizontal: 16),
                      height: 48,
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: selectedYear,
                          icon: Icon(Icons.keyboard_arrow_down,
                              color: colors.primary),
                          isExpanded: true,
                          dropdownColor: colors.backgroundContainer,
                          style: textStyles.body2.copyWith(
                            color: colors.secondaryText,
                          ),
                          onChanged: (String? newValue) {
                            setState(() {
                              selectedYear = newValue!;
                            });
                          },
                          items: <String>[
                            'Year',
                            '2024',
                            '2023',
                            '2022',
                            '2021'
                          ].map<DropdownMenuItem<String>>((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(value),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            Container(
              margin: const EdgeInsetsDirectional.only(top: 12),
            ),

            // Salary entries list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsetsDirectional.symmetric(horizontal: 16),
                itemCount: salaryEntries.length,
                itemBuilder: (context, index) {
                  final entry = salaryEntries[index];
                  return GestureDetector(
                    onTap: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder: (context) => SalaryDetailPopup(
                          salaryData: entry,
                        ),
                      );
                    },
                    child: Container(
                      width: 328,
                      margin: const EdgeInsetsDirectional.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(11, 10, 11, 6),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (index == 0)
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  localization.thisMonthsSalary,
                                  style: textStyles.headline4.copyWith(
                                    color: colors.primary,
                                  ),
                                ),
                                Container(
                                  margin:
                                      const EdgeInsetsDirectional.only(top: 8),
                                ),
                              ],
                            ),

                          // Date and status row
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                entry['date'],
                                style: textStyles.body2.copyWith(
                                  color: colors.tertiaryText,
                                ),
                              ),
                              Container(
                                padding: const EdgeInsetsDirectional.symmetric(
                                    horizontal: 16, vertical: 8),
                                decoration: BoxDecoration(
                                  color: colors.successContainer,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  entry['status'],
                                  style: textStyles.body2.copyWith(
                                    color: colors.success,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Container(
                            margin: const EdgeInsetsDirectional.only(top: 8),
                          ),

                          // Salary details card
                          Container(
                            padding: const EdgeInsetsDirectional.symmetric(
                                horizontal: 16,
                                vertical:
                                    12), // Reduced vertical padding slightly
                            decoration: BoxDecoration(
                              color: colors.backgroundContainer,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: colors.strokeColor,
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  localization.netSalary,
                                  style: textStyles.body2.copyWith(
                                    color: colors.tertiaryText,
                                  ),
                                ),
                                Text(
                                  entry['amount'],
                                  style: textStyles.body2.copyWith(
                                    color: colors.secondaryText,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

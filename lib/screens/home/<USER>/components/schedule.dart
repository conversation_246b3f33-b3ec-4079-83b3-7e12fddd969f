import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';

class Schedule extends StatefulWidget {
  final VoidCallback? onBack;

  const Schedule({
    super.key,
    this.onBack,
  });

  @override
  State<Schedule> createState() => _SchedulePopupState();
}

class _SchedulePopupState extends State<Schedule> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header with back button and title
            Container(
              padding: const EdgeInsetsDirectional.all(16.0),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: widget.onBack ?? () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsetsDirectional.all(8),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        DirectionHelpers.getBackArrowIcon(context),
                        color: colors.primaryText,
                        size: 20,
                      ),
                    ),
                  ),
                  Container(
                      margin: const EdgeInsetsDirectional.only(start: 16)),
                  Text(
                    localization.schedule,
                    style: textStyles.headline4.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ],
              ),
            ),

            Expanded(
              child: ListView(
                padding: const EdgeInsetsDirectional.symmetric(vertical: 1),
                children: [
                  _buildScheduleCard1(context),
                  _buildScheduleCard2(context),
                  _buildScheduleCard1(context),
                  _buildScheduleCard1(context),
                  _buildScheduleCard2(context),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Schedule Card 1
Widget _buildScheduleCard1(BuildContext context) {
  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;
  final textStyles = theme.extension<TextStyles>()!;
  final localization = AppLocalizations.of(context)!;

  return Container(
    width: double.infinity,
    height: 140,
    margin: const EdgeInsetsDirectional.all(8),
    decoration: BoxDecoration(
      color: colors.backgroundContainer,
      borderRadius: BorderRadius.circular(16),
      border: Border.all(
        color: colors.strokeColor,
        width: 1,
      ),
    ),
    child: ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: Align(
        alignment: AlignmentDirectional.topStart,
        child: Container(
          padding: const EdgeInsetsDirectional.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                localization.conferenceCenter,
                style: textStyles.headline4.copyWith(
                  color: colors.primaryText,
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 10),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsetsDirectional.all(0),
                      margin: const EdgeInsetsDirectional.only(end: 8),
                      child: SvgPicture.asset(
                        'assets/icons/workspace_screen/calendar_minimal.svg',
                        height: 16,
                        width: 16,
                        colorFilter: ColorFilter.mode(
                          colors.primaryText,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsetsDirectional.all(0),
                      child: Text(
                        localization.conferenceDate,
                        style: textStyles.body2.copyWith(
                          color: colors.tertiaryText,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 10),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsetsDirectional.all(0),
                      margin: const EdgeInsetsDirectional.only(end: 8),
                      child: SvgPicture.asset(
                        'assets/icons/workspace_screen/clock_circle.svg',
                        height: 16,
                        width: 16,
                        colorFilter: ColorFilter.mode(
                          colors.primaryText,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsetsDirectional.all(0),
                      child: Text(
                        localization.conferenceTime,
                        style: textStyles.body2.copyWith(
                          color: colors.secondaryText,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 10),
                width: double.infinity, // Fill (304px)
                height: 24, // Hug (24px)
                child: Row(
                  mainAxisAlignment:
                      MainAxisAlignment.spaceBetween, // space-between
                  children: [
                    Container(
                      padding: const EdgeInsetsDirectional.all(0),
                      margin: const EdgeInsetsDirectional.only(end: 8),
                      child: SvgPicture.asset(
                        'assets/icons/workspace_screen/map_point.svg',
                        height: 16,
                        width: 16,
                        colorFilter: ColorFilter.mode(
                          colors.primaryText,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsetsDirectional.all(0),
                      child: Text(
                        localization.conferenceLocation,
                        style: textStyles.body2.copyWith(
                          color: colors.tertiaryText,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

// Schedule Card 2
Widget _buildScheduleCard2(BuildContext context) {
  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;
  final textStyles = theme.extension<TextStyles>()!;
  final localization = AppLocalizations.of(context)!;
  return Container(
    width: double.infinity, // Fill (328px)
    height: 190, // figma design - (158px)
    margin: const EdgeInsetsDirectional.all(8),
    decoration: BoxDecoration(
      color: colors.backgroundContainer,
      borderRadius: BorderRadius.circular(12),
      border: Border.all(
        color: colors.strokeColor,
        width: 1,
      ),
    ),
    child: ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Align(
        alignment: AlignmentDirectional.topStart,
        child: Padding(
          padding: const EdgeInsetsDirectional.all(
              14.0), // padding: const EdgeInsetsDirectional.fromLTRB(12, 8, 12, 8), this is according to the figma design but looks ugly
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                localization.conferenceCenter,
                style: textStyles.headline4.copyWith(
                  color: colors.primaryText,
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 10),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsetsDirectional.all(0),
                      margin: const EdgeInsetsDirectional.only(end: 8),
                      child: SvgPicture.asset(
                        'assets/icons/workspace_screen/calendar_minimal.svg',
                        height: 16,
                        width: 16,
                        colorFilter: ColorFilter.mode(
                          colors.primaryText,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsetsDirectional.all(0),
                      child: Text(
                        localization.conferenceDate,
                        style: textStyles.body2.copyWith(
                          color: colors.secondaryText,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 10),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsetsDirectional.all(0),
                      margin: const EdgeInsetsDirectional.only(end: 8),
                      child: SvgPicture.asset(
                        'assets/icons/workspace_screen/clock_circle.svg',
                        height: 16,
                        width: 16,
                        colorFilter: ColorFilter.mode(
                          colors.primaryText,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsetsDirectional.all(0),
                      child: Text(
                        localization.conferenceTime,
                        style: textStyles.body2.copyWith(
                          color: colors.secondaryText,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 10),
                width: double.infinity, // Fill (304px)
                height: 24, // Hug (24px)
                child: Row(
                  mainAxisAlignment:
                      MainAxisAlignment.spaceBetween, // space-between
                  children: [
                    Container(
                      padding: const EdgeInsetsDirectional.all(0),
                      margin: const EdgeInsetsDirectional.only(end: 8),
                      child: SvgPicture.asset(
                        'assets/icons/workspace_screen/map_point.svg',
                        height: 16,
                        width: 16,
                        colorFilter: ColorFilter.mode(
                          colors.primaryText,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsetsDirectional.all(0),
                      child: Text(
                        localization.conferenceLocation,
                        style: textStyles.body2.copyWith(
                          color: colors.tertiaryText,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Action buttons - Reject and Approve
              Container(
                margin: const EdgeInsetsDirectional.only(top: 6),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        style: OutlinedButton.styleFrom(
                          foregroundColor: colors.error,
                          side: BorderSide(color: colors.error, width: 1),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          fixedSize: const Size.fromHeight(32),
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              16, 8, 16, 8),
                        ),
                        onPressed: () {},
                        child: Text(
                          localization.reject,
                          style: textStyles.body2.copyWith(
                            color: colors.error,
                          ),
                        ),
                      ),
                    ),
                    Container(
                        margin: const EdgeInsetsDirectional.only(start: 8)),
                    Expanded(
                      child: OutlinedButton(
                        style: OutlinedButton.styleFrom(
                          foregroundColor: colors.success,
                          side: BorderSide(color: colors.success, width: 1),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          fixedSize: const Size.fromHeight(32),
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              16, 8, 16, 8),
                        ),
                        onPressed: () {},
                        child: Text(
                          localization.approve,
                          style: textStyles.body2.copyWith(
                            color: colors.success,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

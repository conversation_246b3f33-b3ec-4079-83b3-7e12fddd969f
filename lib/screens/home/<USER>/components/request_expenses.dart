import 'dart:io';

import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter_svg/svg.dart';

class RequestExpenses extends StatefulWidget {
  final VoidCallback? onCancel;
  final VoidCallback? onSave;

  const RequestExpenses({
    super.key,
    this.onCancel,
    this.onSave,
  });

  @override
  State<RequestExpenses> createState() => _RequestLeavePopupState();
}

class _RequestLeavePopupState extends State<RequestExpenses> {
  final TextEditingController titleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController amountController = TextEditingController();
  File? selectedFile;

  @override
  void dispose() {
    titleController.dispose();
    descriptionController.dispose();
    amountController.dispose();
    super.dispose();
  }

  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'pdf', 'png', 'jpeg'],
      );

      if (result != null &&
          result.files.isNotEmpty &&
          result.files.single.path != null) {
        setState(() {
          selectedFile = File(result.files.single.path!);
          // You can add code here to display the selected file name
        });
        print("File selected: ${result.files.single.path}");
      } else {
        print("No file selected or file path is null");
      }
    } catch (e) {
      print("Error picking file: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;

    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.only(bottom: bottomInset),
        child: Container(
          decoration: BoxDecoration(
            color: colors.backgroundContainer,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    Labels.requestExpenses,
                    style: textStyles.headline4.copyWith(
                      color: colors.primary,
                    ),
                  ),
                ),
              ),

              // Text input fields
              Container(
                padding: const EdgeInsets.fromLTRB(10, 8, 10, 8),
                child: Column(
                  children: [
                    // Title field
                    TextField(
                      controller: titleController,
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                        labelText: 'Title',
                        floatingLabelBehavior: FloatingLabelBehavior.auto,
                        labelStyle: textStyles.body2.copyWith(
                          color: colors.tertiaryText,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: colors.strokeColor,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: colors.primary,
                          ),
                        ),
                      ),
                      style: textStyles.body2.copyWith(
                        color: colors.primaryText,
                      ),
                    ),

                    Container(
                      height: 8,
                    ),

                    // Description field
                    TextField(
                      controller: descriptionController,
                      maxLines: 3,
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                        labelText: 'Description',
                        floatingLabelBehavior: FloatingLabelBehavior.auto,
                        labelStyle: textStyles.body2.copyWith(
                          color: colors.tertiaryText,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: colors.strokeColor,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: colors.primary,
                          ),
                        ),
                      ),
                      style: textStyles.body2.copyWith(
                        color: colors.primaryText,
                      ),
                    ),

                    Container(
                      height: 8,
                    ),

                    // Amount field
                    TextField(
                      controller: amountController,
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                        labelText: 'Amount IQD',
                        floatingLabelBehavior: FloatingLabelBehavior.auto,
                        labelStyle: textStyles.body2.copyWith(
                          color: colors.tertiaryText,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: colors.strokeColor,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: colors.primary,
                          ),
                        ),
                      ),
                      style: textStyles.body2.copyWith(
                        color: colors.primaryText,
                      ),
                    ),
                  ],
                ),
              ),

              // file picker
              Container(
                margin: const EdgeInsets.fromLTRB(10, 16, 10, 16),
                child: InkWell(
                  onTap: _pickFile,
                  child: DottedBorder(
                    color: colors.strokeColor,
                    strokeWidth: 1,
                    dashPattern: const [3, 2],
                    borderType: BorderType.RRect,
                    radius: const Radius.circular(8),
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 24),
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 48,
                            height: 48,
                            decoration: BoxDecoration(
                              color: colors.primaryVariant,
                              shape: BoxShape.circle,
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: Center(
                                child: SvgPicture.asset(
                                  'assets/icons/workspace_screen/upload.svg',
                                  width: 24,
                                  height: 24,
                                ),
                              ),
                            ),
                          ),
                          Container(
                            height: 12,
                          ),
                          Text(
                            Labels.clickToUpload,
                            style: textStyles.body2.copyWith(
                              color: colors.primary,
                            ),
                          ),
                          Container(
                            height: 4,
                          ),
                          Text(
                            Labels.maxFileSize,
                            style: textStyles.body2.copyWith(
                              color: colors.tertiaryText,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 44),
              // Buttons
              Container(
                padding: const EdgeInsets.fromLTRB(10, 8, 10, 24),
                child: Container(
                  height: 44,
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: widget.onSave ?? () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colors.primary,
                      foregroundColor: colors.primaryText,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      Labels.submit,
                      style: textStyles.headline4.copyWith(
                        color: colors.primaryText,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

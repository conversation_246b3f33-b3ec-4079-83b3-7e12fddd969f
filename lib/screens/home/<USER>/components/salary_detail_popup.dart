import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class SalaryDetailPopup extends StatefulWidget {
  final Map<String, dynamic> salaryData;
  final VoidCallback? onClose;

  const SalaryDetailPopup({
    super.key,
    required this.salaryData,
    this.onClose,
  });

  @override
  State<SalaryDetailPopup> createState() => _SalaryDetailPopupState();
}

class _SalaryDetailPopupState extends State<SalaryDetailPopup> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Container(
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      // height: 333,
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    Labels.salary,
                    style: textStyles.headline4.copyWith(
                      color: colors.primary,
                    ),
                  ),
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: colors.successContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          widget.salaryData['status'],
                          style: textStyles.body2.copyWith(
                            color: colors.success,
                          ),
                        ),
                        Text(
                          widget.salaryData['date'],
                          style: textStyles.body2.copyWith(
                            color: colors.tertiaryText,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    margin: const EdgeInsets.only(top: 12),
                  ),

                  // Salary detail rows which will be changed later accordingly
                  _buildSalaryDetailRow(
                      context, "Basic Salary", "IQD 1,000,000"),
                  _buildSalaryDetailRow(context, "Allowances", "IQD 100,000"),
                  _buildSalaryDetailRow(context, "Deductions", "IQD 100,000"),
                  _buildSalaryDetailRow(context, "Net Salary", "IQD 1,000,000"),
                  _buildPaySlip(context, "", ""),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// salary detail wali rows
Widget _buildSalaryDetailRow(BuildContext context, String label, String value) {
  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;
  final textStyles = theme.extension<TextStyles>()!;
  return Container(
    margin: const EdgeInsets.only(bottom: 12),
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    decoration: BoxDecoration(
      color: colors.backgroundContainer,
      borderRadius: BorderRadius.circular(8),
      border: Border.all(
        color: colors.strokeColor,
        width: 1,
      ),
    ),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: textStyles.body2.copyWith(
            color: colors.tertiaryText,
          ),
        ),
        Text(
          value,
          style: textStyles.body2.copyWith(
            color: colors.secondaryText,
          ),
        ),
      ],
    ),
  );
}

// download payslip component
Widget _buildPaySlip(BuildContext context, String label, String value) {
  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;
  final textStyles = theme.extension<TextStyles>()!;
  return Container(
    margin: const EdgeInsets.only(bottom: 12),
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    decoration: BoxDecoration(
      color: colors.backgroundContainer,
      borderRadius: BorderRadius.circular(8),
      border: Border.all(
        color: colors.strokeColor,
        width: 1,
      ),
    ),
    child: Row(
      children: [
        SvgPicture.asset(
          'assets/icons/salary_screen/document-payslip.svg',
          width: 24,
          height: 24,
          colorFilter: ColorFilter.mode(
            colors.primaryText,
            BlendMode.srcIn,
          ),
        ),
        Container(margin: const EdgeInsets.only(left: 12)),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                Labels.paySlip,
                style: textStyles.body2.copyWith(
                  color: colors.primaryText,
                ),
              ),
              Text(
                Labels.maxFileSizePdf,
                style: textStyles.body2.copyWith(
                  color: colors.tertiaryText,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          icon: SvgPicture.asset(
            'assets/icons/salary_screen/download.svg',
            width: 24,
            height: 24,
            colorFilter: ColorFilter.mode(
              colors.primaryText,
              BlendMode.srcIn,
            ),
          ),
          onPressed: () {
            // Add download functionality here
          },
        ),
      ],
    ),
  );
}

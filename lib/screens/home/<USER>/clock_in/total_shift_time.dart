import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

class TotalShiftTime extends StatefulWidget {
  const TotalShiftTime({super.key});

  @override
  State<TotalShiftTime> createState() => _TotalShiftTimeState();
}

class _TotalShiftTimeState extends State<TotalShiftTime> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Column(
      children: [
        Align(
          alignment: AlignmentDirectional.topStart,
          child: Padding(
            padding: const EdgeInsetsDirectional.symmetric(
                vertical: 4, horizontal: 12.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: colors.background,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.schedule_outlined,
                    color: colors.primary,
                    size: 36,
                  ),
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(start: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        localization.clockInTime,
                        style: textStyles.headline.copyWith(
                          color: colors.primaryText,
                          fontSize: 24,
                        ),
                      ),
                      Text(
                        localization.totalShiftTime,
                        style: textStyles.body3.copyWith(
                          color: colors.tertiaryText,
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/home/<USER>/clock_in/shift_buttons.dart';
import 'package:ako_basma/screens/home/<USER>/clock_in/shift_timings.dart';
import 'package:ako_basma/screens/home/<USER>/clock_in/total_shift_time.dart';
import 'package:ako_basma/screens/home/<USER>/clock_out/clock_out_button.dart';
import 'package:ako_basma/styles/colors.dart';

import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

class ClockInDetailsScreen extends StatefulWidget {
  final VoidCallback? onBack;
  final VoidCallback? onEndShift;

  const ClockInDetailsScreen({
    super.key,
    this.onBack,
    this.onEndShift,
  });

  @override
  State<ClockInDetailsScreen> createState() => _ClockInDetailsScreenState();
}

// divider
Widget _buildDivider(ThemeData theme) {
  final colors = theme.extension<AppColors>()!;
  return Padding(
    padding: const EdgeInsetsDirectional.symmetric(horizontal: 12.0),
    child: Divider(height: 1, thickness: 1, color: colors.strokeColor),
  );
}

class _ClockInDetailsScreenState extends State<ClockInDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    final screenWidth = MediaQuery.of(context).size.width;
    final containerWidth = screenWidth - 32; // 16px margin on each side

    return Container(
      margin:
          const EdgeInsetsDirectional.symmetric(horizontal: 16, vertical: 14),
      width: containerWidth,
      height: 450,
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        border: Border.all(
          color: colors.primaryVariant,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      // location text
      child: Column(
        children: [
          Align(
            alignment: AlignmentDirectional.topStart,
            child: Padding(
              padding: const EdgeInsetsDirectional.all(13.0),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsetsDirectional.all(5),
                    decoration: BoxDecoration(
                      color: colors.primaryVariant,
                      borderRadius: BorderRadius.circular(13),
                    ),
                    width: 24,
                    height: 24,
                    child: Icon(
                      Icons.location_on_outlined,
                      color: colors.primary,
                      size: 14,
                    ),
                  ),
                  Container(
                    margin: const EdgeInsetsDirectional.only(start: 10),
                    child: Text(
                      localization.location,
                      style: textStyles.body3.copyWith(
                        color: colors.secondaryText,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          _buildDivider(theme),
          Container(
            margin: const EdgeInsetsDirectional.symmetric(
                horizontal: 2, vertical: 2),
            child: const Row(
              children: [TotalShiftTime()],
            ),
          ),
          _buildDivider(theme),
          const SizedBox(height: 8),
          _buildTimeDuration(context, theme, localization.overTime, '3h 30m'),
          _buildTimeDuration(context, theme, localization.breakTime, '30m'),
          _buildTimeDuration(context, theme, localization.delay, '5m'),
          Container(
            width: double.infinity,
            margin: const EdgeInsetsDirectional.symmetric(
                horizontal: 13, vertical: 12),
            child: const ShiftTimings(),
          ),
          Container(
            margin: const EdgeInsetsDirectional.symmetric(
                horizontal: 6, vertical: 12),
            child: Column(
              children: [
                ShiftButtons(
                  onEndShiftPressed: widget.onEndShift,
                ),
                // ClockOutButton(
                //   onPressed: () {},
                // ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

Widget _buildTimeDuration(BuildContext context, ThemeData theme,
    String timeDurationText, String actualTime) {
  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;
  final textStyles = theme.extension<TextStyles>()!;
  return Container(
    margin: const EdgeInsetsDirectional.symmetric(horizontal: 13, vertical: 4),
    width: double.infinity,
    child: Container(
      padding: const EdgeInsetsDirectional.fromSTEB(8, 4, 8, 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colors.primaryVariant,
          width: 1,
        ),
        color: colors.background,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              timeDurationText,
              style: textStyles.body3.copyWith(color: colors.secondaryText),
            ),
          ),
          Text(
            actualTime,
            style: textStyles.body3.copyWith(color: colors.secondaryText),
          ),
        ],
      ),
    ),
  );
}

import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';

import 'package:flutter/material.dart';

import 'dart:math' as math;

import 'package:flutter_svg/svg.dart';
import 'package:solar_icons/solar_icons.dart';

class ClockInButton extends StatefulWidget {
  final VoidCallback? onPressed;

  const ClockInButton({
    super.key,
    this.onPressed,
  });

  @override
  State<ClockInButton> createState() => _ClockInButtonState();
}

class _ClockInButtonState extends State<ClockInButton> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Column(
      children: [
        Transform(
          alignment: Alignment.center,
          transform: Matrix4.rotationZ(
            math.pi / 4,
          ),
          child: Container(
            margin: const EdgeInsetsDirectional.symmetric(
                vertical: 28, horizontal: 28),
            width: 120,
            height: 120,
            child: ElevatedButton(
              onPressed: widget.onPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: colors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsetsDirectional.all(4),
              ),
              child: Center(
                child: Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.rotationZ(
                    -math.pi / 4,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Clock icon with proper centering
                      Icon(
                        SolarIconsOutline.clockCircle,
                        color: Colors.white,
                        size: 28,
                      ),
                      // Clock in text with flexible sizing to prevent cutoff
                      Container(
                        margin: const EdgeInsetsDirectional.only(top: 6),
                        child: Text(
                          localization.clockIn,
                          style: textStyles.textButton.copyWith(
                            color: Colors.white,
                            fontSize: 18,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

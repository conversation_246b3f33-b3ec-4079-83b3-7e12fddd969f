import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/home/<USER>/components/company_news_popup.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:carousel_slider/carousel_slider.dart';

class CompanyNews extends StatelessWidget {
  const CompanyNews({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    // 4 company news images
    final items = List<String>.filled(4, 'assets/images/news.png');

    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                Labels.companyNews,
                style: textStyles.headline3.copyWith(
                  color: colors.primaryText,
                ),
              ),
              GestureDetector(
                onTap: () => _showAllNews(context),
                child: Text(
                  Labels.showAll,
                  style: textStyles.body2.copyWith(
                    color: colors.primary,
                  ),
                ),
              ),
            ],
          ),
          Container(
            height: 12,
          ),
          CarouselSlider(
            items: items.map((path) {
              return GestureDetector(
                onTap: () => _showAllNews(context),
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4.0),
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: colors.primaryVariant,
                      width: 1,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Image.asset(
                      path,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                    ),
                  ),
                ),
              );
            }).toList(),
            options: CarouselOptions(
              height: 220,
              viewportFraction: 0.8,
              enlargeCenterPage: true,
              enableInfiniteScroll: true,
              autoPlay: true,
              autoPlayInterval: const Duration(seconds: 3),
            ),
          ),
        ],
      ),
    );
  }

  void _showAllNews(BuildContext context) {
    showAdaptivePopup(
      context,
      (ctx, sc) => AllCompanyNewsScreen(
        onBack: () => Navigator.pop(ctx),
      ),
      isDismissible: false,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );
  }
}

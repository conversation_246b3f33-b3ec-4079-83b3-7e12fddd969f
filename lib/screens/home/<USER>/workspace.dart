import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/home/<USER>/company_news.dart';
import 'package:ako_basma/screens/home/<USER>/components/request_expenses.dart';
import 'package:ako_basma/screens/home/<USER>/components/request_leave_popup.dart';
import 'package:ako_basma/screens/home/<USER>/components/salary_popup.dart';
import 'package:ako_basma/screens/home/<USER>/components/schedule.dart';
import 'package:ako_basma/screens/home/<USER>/timesheet_screen/timesheet_popup.dart';
import 'package:ako_basma/screens/home/<USER>/my_tasks.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'workspace_cards.dart';

class Workspace extends StatelessWidget {
  const Workspace({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Padding(
      padding: const EdgeInsets.all(8), // padding to 16 px
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              buildWorkspaceCard(
                context,
                icon: SvgPicture.asset(
                  'assets/icons/workspace_screen/request_leave.svg',
                  width: 24,
                  height: 24,
                  color: colors.primary,
                ),
                label: Labels.requestLeave,
                onTap: () {
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    backgroundColor: Colors.transparent,
                    useRootNavigator: true,
                    builder: (context) => const RequestLeavePopup(),
                  );
                },
              ),
              // gap of 8px
              buildWorkspaceCard(
                context,
                icon: SvgPicture.asset(
                  'assets/icons/workspace_screen/Vector.svg',
                  width: 24,
                  height: 24,
                  color: colors.primary,
                ),
                label: Labels.timesheet,
                onTap: () {
                  showAdaptivePopup(
                    context,
                    (ctx, sc) => const TimesheetPopup(),
                    isDismissible: false,
                    scrollable: true,
                    contentPadding: EdgeInsets.zero,
                    topRadius: 0,
                    fullScreen: true,
                    useRootNavigator: true,
                  );
                },
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              buildWorkspaceCard(
                context,
                icon: SvgPicture.asset(
                  'assets/icons/workspace_screen/schedule.svg',
                  width: 24,
                  height: 24,
                  color: colors.primary,
                ),
                label: Labels.schedule,
                onTap: () {
                  showAdaptivePopup(
                    context,
                    (ctx, sc) => const Schedule(),
                    isDismissible: false,
                    scrollable: true,
                    contentPadding: EdgeInsets.zero,
                    topRadius: 0,
                    fullScreen: true,
                    useRootNavigator: true,
                  );
                },
              ),
              buildWorkspaceCard(
                context,
                icon: SvgPicture.asset(
                  'assets/icons/workspace_screen/money.svg',
                  width: 24,
                  height: 24,
                  color: colors.primary,
                ),
                label: Labels.salary,
                onTap: () {
                  showAdaptivePopup(
                    context,
                    (ctx, sc) => const SalaryPopup(),
                    isDismissible: false,
                    scrollable: true,
                    contentPadding: EdgeInsets.zero,
                    topRadius: 0,
                    fullScreen: true,
                    useRootNavigator: true,
                  );
                },
              ),
            ],
          ),
          GestureDetector(
            onTap: () {
              showAdaptivePopup(
                context,
                (ctx, sc) => RequestExpenses(
                  onCancel: () => Navigator.pop(ctx),
                ),
                isDismissible: true,
                scrollable: true,
                contentPadding: EdgeInsets.zero,
                initialChildSize: 0.62, // Half of the default 0.9
                minChildSize: 0.4, // Half of the default 0.7
                useRootNavigator: true,
              );
            },
            child: Container(
              width: MediaQuery.of(context).size.width - 32,
              height: 70,
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: colors.backgroundContainer,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: colors.primaryVariant,
                  width: 1,
                ),
              ),
              padding: const EdgeInsets.all(8),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    'assets/icons/workspace_screen/money.svg',
                    width: 24,
                    height: 24,
                    color: colors.primary,
                  ),
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    child: Text(
                      Labels.requestExpenses,
                      textAlign: TextAlign.center,
                      style: textStyles.headline4.copyWith(
                        fontSize: 12,
                        color: colors.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const MyTasks(),
          const CompanyNews(),
        ],
      ),
    );
  }
}

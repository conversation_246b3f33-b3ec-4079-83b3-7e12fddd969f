import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/home/<USER>/components/all_tasks_popup.dart';

import 'package:ako_basma/screens/home/<USER>/components/task_card.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';

class MyTasks extends StatelessWidget {
  const MyTasks({super.key});

  void _showAllTasks(BuildContext context) {
    showAdaptivePopup(
      context,
      (ctx, sc) => AllTasksPopup(
        onBack: () => Navigator.pop(ctx),
      ),
      isDismissible: false,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  Labels.myTasks,
                  style: textStyles.headline4.copyWith(
                    fontSize: 18,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    _showAllTasks(context);
                  },
                  child: Text(
                    Labels.showAll,
                    style: textStyles.body2.copyWith(
                      color: colors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),

          Container(
            margin: const EdgeInsets.only(top: 12),
          ),

          // Task card
          CarouselSlider(
            options: CarouselOptions(
              height: 165,
              enlargeCenterPage: false,
              viewportFraction: 0.90, // Shows a little preview of the next card
              enableInfiniteScroll: false,
              padEnds: false,
            ),
            items: List.generate(
              3,
              (index) => Padding(
                padding: EdgeInsets.only(
                  right: index != 2 ? 12.0 : 0.0,
                ),
                child: const TaskCard(
                  width: 332,
                  height: 165,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

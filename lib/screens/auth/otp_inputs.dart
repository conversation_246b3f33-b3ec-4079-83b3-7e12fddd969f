import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:pinput/pinput.dart';

class OtpInputs extends StatelessWidget {
  const OtpInputs({
    super.key,
    required this.controller,
    this.onChanged,
    this.onCompleted,
  });

  final TextEditingController controller;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onCompleted;

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final basePinTheme = PinTheme(
      width: 48,
      height: 48,
      textStyle: textStyles.headline4,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
    );

    return Pinput(
      length: 6,
      controller: controller,
      autofocus: true,
      textInputAction: TextInputAction.done,
      onChanged: onChanged,
      onCompleted: onCompleted,
      keyboardType: TextInputType.number,

      // Custom pin theme to match the design
      defaultPinTheme: basePinTheme.copyBorderWith(
        border: Border.all(color: colors.tertiaryText, width: 1),
      ),

      // Focused pin theme
      focusedPinTheme: basePinTheme.copyBorderWith(
        border: Border.all(color: colors.primary, width: 1),
      ),

      // Submitted pin theme
      submittedPinTheme: basePinTheme.copyBorderWith(
        border: Border.all(color: colors.primary, width: 1),
      ),

      // Pin spacing
      mainAxisAlignment: MainAxisAlignment.center,
      pinAnimationType: PinAnimationType.fade,
    );
  }
}

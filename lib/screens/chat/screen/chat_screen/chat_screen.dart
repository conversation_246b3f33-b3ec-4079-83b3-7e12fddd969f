import 'package:ako_basma/components/search%20bar/search_bar.dart';
import 'package:ako_basma/components/switch/switch_button_group.dart';
import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/home/<USER>/greeting.dart';
import 'package:ako_basma/screens/chat/screen/chat_screen/components/chat_list.dart';
import 'package:ako_basma/screens/chat/screen/components/new%20group/add_employee.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/components/FAB/floating_action_button.dart';
import 'package:flutter/material.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  /// Handle new group creation action
  void _onNewGroupPressed() {
    // Navigate to Add Employee screen for group creation as full screen modal
    // Use root navigator to bypass the shell route and hide bottom navigation
    Navigator.of(context, rootNavigator: true).push(
      MaterialPageRoute(
        builder: (context) => const AddEmployee(),
        fullscreenDialog:
            true, // This ensures it's treated as a modal that covers everything
      ),
    );
  }

  /// Handle new channel creation action
  void _onNewChannelPressed() {
    // TODO: Implement new channel creation functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
          content: Text('New Channel functionality will be implemented')),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Scaffold(
      backgroundColor: colors.background,
      body: Stack(
        children: [
          // Main content
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Greeting Component at the top
              const Greeting(),

              // Custom Search Bar below greeting
              const CustomSearchBar(
                hintText: "Searching",
                height: 44,
                margin: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),

              // Switch button group (dynamic labels)
              SwitchButtonGroup(
                labels: const [Labels.all, Labels.unread, Labels.teams],
                onTap: (index) {
                  // handle switch selection
                },
              ),

              // Chat list component
              const Expanded(
                child: ChatList(),
              ),
            ],
          ),

          // Floating Action Button with menu options
          FABConfigurations.chatScreenFAB(
            onNewGroup: _onNewGroupPressed,
            onNewChannel: _onNewChannelPressed,
          ),
        ],
      ),
    );
  }
}

/*
// ============================================
// COMMENTED OUT CHAT FUNCTIONALITY FOR LATER
// ============================================

// UNCOMMENT THESE IMPORTS WHEN YOU NEED TO USE THE CHAT FUNCTIONALITY:
// import 'package:ako_basma/screens/chat/constant.dart';
// import 'package:ako_basma/screens/chat/screen/menu.dart';  
// import 'package:ako_basma/screens/chat/service/chat.dart';
// import 'package:flutter_chat_ui/flutter_chat_ui.dart';
// import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
// import 'package:flutter_gemini/flutter_gemini.dart';

// UNCOMMENT THESE VARIABLES IN THE STATE CLASS WHEN YOU NEED THE CHAT FUNCTIONALITY:
// bool _processing = false;
// final _messages = <types.TextMessage>[];
// String _lastModelText = '';
// final _chatKey = GlobalKey<ChatState>();

// REPLACE THE CURRENT BUILD METHOD WITH THIS WHEN YOU WANT TO USE CHAT:
/*
@override
Widget build(BuildContext context) {
  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;
  final textStyles = theme.extension<TextStyles>()!;
  return Scaffold(
    body: Chat(
      key: _chatKey,
      scrollToUnreadOptions: ScrollToUnreadOptions(
        scrollOnOpen: false,
      ),
      messages: [..._messages.reversed.toList()],
      onSendPressed: (text) => _sendMessage(text.text),
      user: types.User(id: 'user'),
      typingIndicatorOptions: TypingIndicatorOptions(
          typingUsers: _processing ? [botUser] : [],
          typingMode: TypingIndicatorMode.name),
      theme: DarkChatTheme(
        backgroundColor: colors.background,
        inputMargin: const EdgeInsets.all(10),
        secondaryColor: colors.backgroundContainer,
        primaryColor: colors.primary,
        inputBackgroundColor: colors.backgroundContainer,
        inputBorderRadius: BorderRadius.circular(20),
      ),
      emptyState: Center(
        child: Column(mainAxisSize: MainAxisSize.min, children: [
          const Text(
            '✨',
            style: TextStyle(
              fontSize: 40,
            ),
          ),
          Text(
            'It\'s quite lonely here.',
            style: textStyles.body.copyWith(
              color: colors.primaryText,
            ),
          )
        ]),
      ),
    ),
    floatingActionButtonLocation: FloatingActionButtonLocation.startTop,
    floatingActionButton: IconButton.filledTonal(
        onPressed: () {
          showModalBottomSheet(
            context: context,
            builder: (ctx) {
              return AppMenu();
            },
          );
        },
        icon: Icon(Icons.menu)),
  );
}
*/

// ADD THESE METHODS TO THE STATE CLASS WHEN YOU NEED CHAT FUNCTIONALITY:
/*
void _sendMessage(String text) async {
  setState(() {
    _messages
        .add(types.TextMessage(id: _lastId(), author: humanUser, text: text));
    _processing = true;
  });
  final gemini = Gemini.instance;
  final result = await gemini.chat(
    _messages
        .map(
          (e) => Content(
            parts: [Parts(text: e.text)],
            role: e.author.id,
          ),
        )
        .toList(),
  );
  var reply;
  try {
    reply = result?.output ?? 'No response received';
  } catch (e) {
    reply = e.toString();
  }

  setState(() {
    _processing = false;
    _messages.add(types.TextMessage(
        id: (_messages.isNotEmpty ? ((int.parse(_messages.last.id)) + 1) : 0)
            .toString(),
        author: botUser,
        text: reply));
    _chatKey.currentState?.scrollToMessage(_lastId(1));
  });
}

String _lastId([int skip = 0]) =>
    ((_messages.isNotEmpty ? ((int.parse(_messages.last.id)) + 1) : 0) - skip)
        .toString();
*/
*/

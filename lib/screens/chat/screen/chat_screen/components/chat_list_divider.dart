import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';

class ChatListDivider extends StatelessWidget {
  const ChatListDivider({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Divider(
        color: colors.strokeColor,
        thickness: 1,
        height: 1,
      ),
    );
  }
}

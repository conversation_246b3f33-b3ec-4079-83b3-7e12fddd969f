import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';

class ChatListItem extends StatelessWidget {
  final String name;
  final String message;
  final String time;
  final String profileImage;
  final bool isRead;
  final bool isOnline;
  final bool isTyping;
  final VoidCallback onTap;

  const ChatListItem({
    super.key,
    required this.name,
    required this.message,
    required this.time,
    required this.profileImage,
    required this.isRead,
    this.isOnline = false,
    this.isTyping = false,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        splashFactory: NoSplash.splashFactory,
        child: Container(
          padding: const EdgeInsetsDirectional.symmetric(
              horizontal: 18, vertical: 16),
          child: Row(
            children: [
              // Profile Image
              Stack(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: colors.primaryVariant,
                    ),
                    child: ClipOval(
                      child: Image.asset(
                        profileImage,
                        width: 50,
                        height: 50,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          // Fallback to initials if image fails to load
                          return Container(
                            color: colors.primary.withOpacity(0.1),
                            child: Center(
                              child: Text(
                                name.isNotEmpty ? name[0].toUpperCase() : 'U',
                                style: textStyles.headline4.copyWith(
                                  color: colors.primary,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  // Online indicator
                  if (isOnline)
                    Positioned(
                      bottom: 1,
                      right: 1,
                      child: Container(
                        width: 14,
                        height: 14,
                        decoration: BoxDecoration(
                          color: colors.success,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: colors.background,
                            width: 2,
                          ),
                        ),
                      ),
                    ),
                ],
              ),

              // Chat content
              Expanded(
                child: Container(
                  margin: const EdgeInsetsDirectional.only(start: 12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Name and time row
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              name,
                              style: textStyles.body.copyWith(
                                color: colors.primaryText,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Text(
                            time,
                            style: textStyles.body3.copyWith(
                              color: colors.tertiaryText,
                            ),
                          ),
                        ],
                      ),

                      // Message
                      Container(
                        margin: const EdgeInsetsDirectional.only(top: 4),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                isTyping ? 'Typing...' : message,
                                style: textStyles.body2.copyWith(
                                  color: isTyping
                                      ? colors.success
                                      : isRead
                                          ? colors.secondaryText
                                          : colors.primaryText,
                                  fontWeight: isRead
                                      ? FontWeight.normal
                                      : FontWeight.w500,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                            // Read status indicator (double checkmarks)
                            if (!isRead)
                              Container(
                                margin:
                                    const EdgeInsetsDirectional.only(start: 8),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.done_all,
                                      size: 16,
                                      color: colors.tertiaryText,
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

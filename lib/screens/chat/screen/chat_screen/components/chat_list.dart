import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ako_basma/screens/chat/screen/chat_screen/components/chat_list_item.dart';
import 'package:ako_basma/screens/chat/screen/chat_screen/components/chat_list_divider.dart';
import 'package:ako_basma/styles/theme.dart';

class ChatList extends StatefulWidget {
  const ChatList({super.key});

  @override
  State<ChatList> createState() => _ChatListState();
}

class _ChatListState extends State<ChatList> {
  // Sample chat data - in real app this would come from API/database
  final List<Map<String, dynamic>> _chatData = [
    {
      'name': '<PERSON><PERSON><PERSON>',
      'message': 'Great! 😊',
      'time': '12:00 PM',
      'profileImage': 'assets/images/person.png',
      'isRead': true,
    },
    {
      'name': '<PERSON><PERSON>',
      'message': 'Hello! Everyone',
      'time': '4:30 PM',
      'profileImage': 'assets/images/person.png',
      'isRead': false,
    },
    {
      'name': '<PERSON>',
      'message': 'How are you doing?',
      'time': '2:15 PM',
      'profileImage': 'assets/images/person.png',
      'isRead': true,
    },
    {
      'name': 'John Smith',
      'message': 'Let\'s meet tomorrow',
      'time': '1:45 PM',
      'profileImage': 'assets/images/person.png',
      'isRead': false,
    },
    {
      'name': 'Maria Garcia',
      'message': 'Thank you for your help!',
      'time': '11:30 AM',
      'profileImage': 'assets/images/person.png',
      'isRead': true,
    },
    {
      'name': 'Ahmed Ali',
      'message': 'See you soon',
      'time': '10:20 AM',
      'profileImage': 'assets/images/person.png',
      'isRead': false,
    },
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;

    return Container(
      color: colors.background,
      child: ListView.builder(
        padding: EdgeInsets.zero,
        itemCount: _chatData.length * 2 - 1, // Account for dividers
        itemBuilder: (context, index) {
          if (index.isOdd) {
            // Show divider for odd indices
            return const ChatListDivider();
          }

          // Show chat item for even indices
          final chatIndex = index ~/ 2;
          final chatItem = _chatData[chatIndex];

          return ChatListItem(
            name: chatItem['name'],
            message: chatItem['message'],
            time: chatItem['time'],
            profileImage: chatItem['profileImage'],
            isRead: chatItem['isRead'],
            isOnline: chatIndex == 0, // First item shows online status
            isTyping: chatIndex == 0, // First item shows typing indicator
            onTap: () {
              // Navigate to individual chat screen with user data
              context.push(
                '/individual-chat?userName=${Uri.encodeComponent(chatItem['name'])}'
                '&userImage=${Uri.encodeComponent(chatItem['profileImage'])}'
                '&lastSeen=${Uri.encodeComponent(chatItem['time'])}'
                '&isOnline=${chatIndex == 0}',
              );
            },
          );
        },
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:ako_basma/labels.dart';
import 'package:ako_basma/components/search bar/search_bar.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/screens/chat/screen/components/new group/create_group.dart';

class AddEmployee extends StatefulWidget {
  const AddEmployee({super.key});

  @override
  State<AddEmployee> createState() => _AddEmployeeState();
}

class _AddEmployeeState extends State<AddEmployee> {
  // List to track selected employees
  final Set<int> _selectedEmployees = {};

  // Generated employee data - in real app this would come from API
  late final List<Map<String, dynamic>> _employees;

  @override
  void initState() {
    super.initState();
    // Generate 12 employees with different names for demo
    _employees = List.generate(12, (index) {
      final names = [
        '<PERSON><PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>'
      ];
      return {
        'name': names[index],
        'profileImage': 'assets/images/person.png',
      };
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Back button
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: colors.background,
                        borderRadius: BorderRadius.circular(8),
                        // border: Border.all(
                        //   color: colors.strokeColor,
                        //   width: 1,
                        // ),
                      ),
                      child: Icon(
                        Icons.arrow_back_ios_new,
                        color: colors.primaryText,
                        size: 20,
                      ),
                    ),
                  ),

                  // Title and step indicator
                  Column(
                    children: [
                      Text(
                        Labels.addEmployee,
                        style: textStyles.body2.copyWith(
                          color: colors.primaryText,
                        ),
                      ),
                      Container(
                        margin: const EdgeInsets.only(top: 4),
                        child: Text(
                          '02/26',
                          style: textStyles.body3.copyWith(
                            color: colors.tertiaryText,
                          ),
                        ),
                      ),
                    ],
                  ),

                  // Next button
                  GestureDetector(
                    onTap: () {
                      // Navigate to create group screen only if employees are selected
                      if (_selectedEmployees.isNotEmpty) {
                        // Get selected employees data
                        final selectedEmployeesData = _selectedEmployees
                            .map((index) => _employees[index])
                            .toList();

                        // Navigate to CreateGroup screen
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => CreateGroup(
                              selectedEmployees: selectedEmployeesData,
                            ),
                          ),
                        );
                      }
                    },
                    child: Text(
                      Labels.next,
                      style: textStyles.textButton.copyWith(
                        color: _selectedEmployees.isNotEmpty
                            ? colors.primary
                            : colors.secondaryText,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Search bar
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 10),
              child: const CustomSearchBar(
                hintText: "Search",
                height: 44,
              ),
            ),

            // Selected employees at the top (if any)
            if (_selectedEmployees.isNotEmpty)
              Container(
                margin:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Row(
                  children: [
                    // Show first 2 selected employees as avatars with names
                    ..._selectedEmployees.take(2).map((index) {
                      final employee = _employees[index];
                      return Container(
                        margin: const EdgeInsets.only(right: 16),
                        child: _buildSelectedEmployeeAvatar(
                            employee, colors, textStyles),
                      );
                    }),
                  ],
                ),
              ),

            // Employee list
            Expanded(
              child: Container(
                margin: EdgeInsets.fromLTRB(
                    16,
                    _selectedEmployees.isEmpty
                        ? 12
                        : 0, // Add top margin only when no employees selected
                    16,
                    0),
                child: ListView.builder(
                  itemCount: _employees.length,
                  itemBuilder: (context, index) {
                    final employee = _employees[index];
                    final isSelected = _selectedEmployees.contains(index);

                    return Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Row(
                        children: [
                          // Profile image
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: colors.primaryVariant,
                            ),
                            child: ClipOval(
                              child: Image.asset(
                                employee['profileImage'],
                                width: 50,
                                height: 50,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  // Fallback to initials if image fails to load
                                  return Container(
                                    color: colors.primary.withOpacity(0.1),
                                    child: Center(
                                      child: Text(
                                        employee['name'].isNotEmpty
                                            ? employee['name'][0].toUpperCase()
                                            : 'U',
                                        style: textStyles.headline4.copyWith(
                                          color: colors.primary,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),

                          // Name
                          Expanded(
                            child: Container(
                              margin: const EdgeInsets.only(left: 16),
                              child: Text(
                                employee['name'],
                                style: textStyles.headline4.copyWith(
                                  color: colors.primaryText,
                                ),
                              ),
                            ),
                          ),

                          // Checkbox
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                if (isSelected) {
                                  _selectedEmployees.remove(index);
                                } else {
                                  _selectedEmployees.add(index);
                                }
                              });
                            },
                            child: Container(
                              width: 20,
                              height: 20,
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? colors.primary
                                    : Colors.transparent,
                                border: Border.all(
                                  color: colors.primary,
                                  width: 1,
                                ),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: isSelected
                                  ? const Icon(
                                      Icons.check,
                                      size: 16,
                                      color: Colors.white,
                                    )
                                  : null,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build selected employee avatar with close button and name
  Widget _buildSelectedEmployeeAvatar(
      Map<String, dynamic> employee, AppColors colors, TextStyles textStyles) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Avatar with close button
        Stack(
          children: [
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: colors.primaryVariant,
              ),
              child: ClipOval(
                child: Image.asset(
                  employee['profileImage'],
                  width: 64,
                  height: 64,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: colors.primaryVariant,
                      child: Center(
                        child: Text(
                          employee['name'].isNotEmpty
                              ? employee['name'][0].toUpperCase()
                              : 'U',
                          style: TextStyle(
                            color: colors.primary,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            // Close button
            Positioned(
              top: -2,
              right: -2,
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    // Remove this employee from selected list
                    final indexToRemove = _selectedEmployees.firstWhere(
                      (index) => _employees[index]['name'] == employee['name'],
                    );
                    _selectedEmployees.remove(indexToRemove);
                  });
                },
                child: Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: colors.background,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: colors.strokeColor,
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.close,
                    size: 12,
                    color: colors.secondaryText,
                  ),
                ),
              ),
            ),
          ],
        ),
        // Employee name below avatar
        Container(
          margin: const EdgeInsets.only(top: 6),
          constraints: const BoxConstraints(maxWidth: 80), // Limit name width
          child: Text(
            employee['name'],
            style: textStyles.body2.copyWith(
              color: colors.primaryText,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/screens/chat/screen/components/new group/group chat/group_chat_header.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/chat_input_field.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/attachment_menu.dart';
import 'package:ako_basma/util/image/image_service.dart';
import 'package:ako_basma/util/document/document_service.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/contact_selection_modal.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/location_sharing_modal.dart';

class GroupChatScreen extends StatefulWidget {
  final String groupName;
  final File? groupImage;
  final List<Map<String, dynamic>> members;

  const GroupChatScreen({
    super.key,
    required this.groupName,
    this.groupImage,
    required this.members,
  });

  @override
  State<GroupChatScreen> createState() => _GroupChatScreenState();
}

class _GroupChatScreenState extends State<GroupChatScreen> {
  bool _showAttachmentMenu = false;

  /// Toggles the attachment menu visibility
  void _toggleAttachmentMenu() {
    setState(() {
      _showAttachmentMenu = !_showAttachmentMenu;
    });
  }

  /// Handle camera functionality
  Future<void> _handleCameraPress() async {
    try {
      _toggleAttachmentMenu(); // Close attachment menu first

      // Use the reusable image service to capture photo
      final File? photo = await ImageService.captureFromCamera();

      if (photo != null) {
        // Handle the captured photo
        // TODO: Process and send the image in group chat
        // implement image sending logic here

        // Show success feedback using image service
        if (mounted) {
          ImageService.showSuccessMessage(
              context, 'Photo captured successfully!');
        }
      }
    } catch (e) {
      // Handle any errors during camera operation using image service
      if (mounted) {
        ImageService.showErrorMessage(
            context, 'Failed to capture photo: ${e.toString()}');
      }
    }
  }

  /// Handle gallery functionality
  Future<void> _handleGalleryPress() async {
    try {
      _toggleAttachmentMenu(); // Close attachment menu first

      // Use the reusable image service to select from gallery
      final File? image = await ImageService.selectFromGallery();

      if (image != null) {
        // Handle the selected image
        // TODO: Process and send the image in group chat

        // Show success feedback using image service
        if (mounted) {
          ImageService.showSuccessMessage(
              context, 'Image selected successfully!');
        }
      }
    } catch (e) {
      // Handle any errors during gallery operation using image service
      if (mounted) {
        ImageService.showErrorMessage(
            context, 'Failed to select image: ${e.toString()}');
      }
    }
  }

  /// Handle document functionality - opens file picker to select document
  Future<void> _handleDocumentPress() async {
    try {
      _toggleAttachmentMenu(); // Close attachment menu first

      // Use the reusable document service to select document
      final File? document = await DocumentService
          .selectDocument(); // currently the document is storing in the document variable

      if (document != null) {
        // Handle the selected document
        final fileName = DocumentService.getFileName(document.path);
        final fileSize = DocumentService.getFileSize(document);
        print('Document selected: ${document.path}');
        print('File name: $fileName, Size: $fileSize');
        // TODO: Process and send the document in group chat

        // Show success feedback using document service
        if (mounted) {
          DocumentService.showSuccessMessage(
              context, 'Document "$fileName" selected successfully!');
        }
      }
    } catch (e) {
      // Handle any errors during document operation
      if (mounted) {
        DocumentService.showErrorMessage(
            context, 'Failed to select document: ${e.toString()}');
      }
    }
  }

  /// Handle contact selection functionality - opens contact selection popup
  void _handleContactPress() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) => ContactSelectionModal(
        onContactsSelected: (selectedContacts) {
          // Handle selected contacts
          if (selectedContacts.isNotEmpty) {
            print(
                'Selected contacts: ${selectedContacts.map((c) => c.name).join(', ')}');
            // TODO: Implement contact sharing logic for group
          }
        },
      ),
    );
  }

  /// Handle location sharing functionality - opens location sharing modal
  void _handleLocationPress() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) => LocationSharingModal(
        onLocationSelected: (locationType, locationData) {
          // Handle location sharing

          // TODO: Implement location sharing logic for group
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return GestureDetector(
      // Close attachment menu when tapping outside
      onTap: () {
        if (_showAttachmentMenu) {
          _toggleAttachmentMenu();
        }
      },
      child: Scaffold(
        backgroundColor: colors.background,
        body: Stack(
          children: [
            // Main chat layout
            Column(
              children: [
                // Group Chat Header
                GroupChatHeader(
                  groupName: widget.groupName,
                  groupImage: widget.groupImage,
                  memberCount: widget.members.length,
                  onBackPressed: () {
                    // Navigate directly to chat screen (using root navigator to bypass any modal routes)
                    Navigator.of(context, rootNavigator: true)
                        .popUntil((route) => route.isFirst);
                  },
                ),

                // Messages area with initial group creation message
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      children: [
                        // Today divider
                        Container(
                          margin: const EdgeInsetsDirectional.symmetric(
                              vertical: 20),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                margin: const EdgeInsetsDirectional.symmetric(
                                    horizontal: 16),
                                padding: const EdgeInsetsDirectional.symmetric(
                                  horizontal: 12,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: colors.background,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: colors.strokeColor,
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  Labels.today,
                                  style: textStyles.body3.copyWith(
                                    color: colors.tertiaryText,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Group creation message
                        Container(
                          margin: const EdgeInsetsDirectional.only(bottom: 20),
                          child: Text(
                            Labels.youCreatedGroup,
                            style: textStyles.body2.copyWith(
                              color: colors.tertiaryText,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),

                        // Space for future messages (currently empty)
                        const Expanded(child: SizedBox()),
                      ],
                    ),
                  ),
                ),

                // Chat input field at bottom
                ChatInputField(
                  onSendMessage: (message) {
                    // Handle sending message in group
                    // TODO: Implement group message sending logic
                    print('Group message sent: $message');
                  },
                  onAttachmentPressed: _toggleAttachmentMenu,
                  onVoicePressed: () {
                    // TODO: Handle voice message in group
                    // implement voice message sending logic here
                  },
                ),
              ],
            ),

            // Attachment menu overlay
            if (_showAttachmentMenu)
              Positioned(
                bottom: 100, // Position above the input field
                left: MediaQuery.of(context).size.width *
                    0.02, // Center with same margin as input field
                right: MediaQuery.of(context).size.width * 0.02,
                child: AttachmentMenu(
                  onCameraPressed: _handleCameraPress,
                  onRecordPressed: () {
                    // Handle record action
                    _toggleAttachmentMenu();
                    print('Record pressed in group');
                  },
                  onContactPressed: () {
                    // Handle contact action
                    _toggleAttachmentMenu();
                    _handleContactPress();
                  },
                  onGalleryPressed: _handleGalleryPress,
                  onLocationPressed: () {
                    // Handle location action
                    _toggleAttachmentMenu();
                    _handleLocationPress();
                  },
                  onDocumentPressed: _handleDocumentPress,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

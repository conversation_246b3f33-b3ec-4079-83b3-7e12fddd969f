import 'package:flutter/material.dart';
import 'dart:io';
import 'package:ako_basma/styles/theme.dart';

/// Group chat header component
/// Displays group photo, name, member count, and back button
class GroupChatHeader extends StatelessWidget {
  final String groupName;
  final File? groupImage;
  final int memberCount;
  final VoidCallback onBackPressed;

  const GroupChatHeader({
    super.key,
    required this.groupName,
    this.groupImage,
    required this.memberCount,
    required this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      width: screenWidth,
      color: colors.backgroundContainer,
      padding: EdgeInsets.only(
        left: 28,
        right: 16,
        top: MediaQuery.of(context).padding.top + 8, // Blend with status bar
        bottom: 12,
      ),
      child: Row(
        children: [
          // Back button
          Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: onBackPressed,
              child: Container(
                padding: const EdgeInsets.all(8),
                child: Icon(
                  Icons.arrow_back_ios,
                  size: 20,
                  color: colors.primaryText,
                ),
              ),
            ),
          ),

          // Group profile section
          Expanded(
            child: Container(
              margin: const EdgeInsets.only(left: 4),
              child: Row(
                children: [
                  // Group image
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: colors.primaryVariant,
                    ),
                    child: ClipOval(
                      child: groupImage != null
                          ? Image.file(
                              groupImage!,
                              width: 40,
                              height: 40,
                              fit: BoxFit.cover,
                            )
                          : Container(
                              color: colors.primaryVariant,
                              child: Center(
                                child: Text(
                                  groupName.isNotEmpty
                                      ? groupName[0].toUpperCase()
                                      : 'G',
                                  style: textStyles.headline4.copyWith(
                                    color: colors.primary,
                                  ),
                                ),
                              ),
                            ),
                    ),
                  ),

                  // Group name and member count
                  Expanded(
                    child: Container(
                      margin: const EdgeInsets.only(left: 12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Group name
                          Text(
                            groupName,
                            style: textStyles.headline4.copyWith(
                              color: colors.primaryText,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),

                          // Member count
                          Container(
                            margin: const EdgeInsets.only(top: 4),
                            child: Text(
                              '$memberCount Members',
                              style: textStyles.body3.copyWith(
                                color: colors.tertiaryText,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

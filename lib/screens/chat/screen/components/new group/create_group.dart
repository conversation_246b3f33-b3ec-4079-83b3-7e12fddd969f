import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/chat/screen/components/new group/group chat/group_chat_screen.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';

class CreateGroup extends StatefulWidget {
  final List<Map<String, dynamic>> selectedEmployees;

  const CreateGroup({
    super.key,
    required this.selectedEmployees,
  });

  @override
  State<CreateGroup> createState() => _CreateGroupState();
}

class _CreateGroupState extends State<CreateGroup> {
  // Text controller for group name input
  final TextEditingController _groupNameController = TextEditingController();
  final TextEditingController _groupDescriptionController =
      TextEditingController();

  // Image picker instance for selecting group photo
  final ImagePicker _picker = ImagePicker();

  // Selected image file for group photo
  File? _selectedGroupImage;

  // Focus node for group name text field
  final FocusNode _groupNameFocusNode = FocusNode();

  @override
  void dispose() {
    _groupNameController.dispose();
    _groupNameFocusNode.dispose();
    _groupDescriptionController.dispose();
    super.dispose();
  }

  /// Handle group photo selection from gallery
  Future<void> _selectGroupPhoto() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80, // Compress image for performance
        maxWidth: 512,
        maxHeight: 512,
      );

      if (image != null) {
        setState(() {
          _selectedGroupImage = File(image.path);
        });
      }
    } catch (e) {
      // Handle image selection error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to select image. Please try again.'),
          ),
        );
      }
    }
  }

  /// Handle group creation action
  void _createGroup() {
    final groupName = _groupNameController.text.trim();

    if (groupName.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a group name'),
        ),
      );
      return;
    }

    // Navigate to group chat screen with group data
    Navigator.of(context, rootNavigator: true).pushReplacement(
      MaterialPageRoute(
        builder: (context) => GroupChatScreen(
          groupName: groupName,
          groupImage: _selectedGroupImage,
          members: widget.selectedEmployees,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsetsDirectional.all(16),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsetsDirectional.all(8),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        DirectionHelpers.getBackArrowIcon(context),
                        color: colors.primaryText,
                        size: 20,
                      ),
                    ),
                  ),
                  Container(
                      margin: const EdgeInsetsDirectional.only(start: 16)),
                  Text(
                    localization.newGroup,
                    style: textStyles.headline4.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ],
              ),
            ),

            // Form content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsetsDirectional.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(height: 16),

                    // Group photo section
                    Center(
                      child: GestureDetector(
                        onTap: _selectGroupPhoto,
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: colors.backgroundContainer,
                            borderRadius: BorderRadius.circular(50),
                            border: Border.all(
                              color: colors.strokeColor,
                              width: 1,
                            ),
                          ),
                          child: _selectedGroupImage != null
                              ? ClipOval(
                                  child: Image.file(
                                    _selectedGroupImage!,
                                    width: 100,
                                    height: 100,
                                    fit: BoxFit.cover,
                                  ),
                                )
                              : Icon(
                                  Icons.camera_alt_outlined,
                                  color: colors.secondaryText,
                                  size: 32,
                                ),
                        ),
                      ),
                    ),

                    Container(height: 24),

                    // Group name field
                    TextField(
                      controller: _groupNameController,
                      decoration: InputDecoration(
                        labelText: localization.groupName,
                        hintText: localization.enterGroupName,
                        filled: true,
                        fillColor: colors.backgroundContainer,
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: colors.strokeColor,
                            width: 1,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: colors.primary,
                            width: 1,
                          ),
                        ),
                      ),
                      style: textStyles.body.copyWith(
                        color: colors.primaryText,
                      ),
                    ),

                    Container(height: 16),

                    // Group description field
                    TextField(
                      controller: _groupDescriptionController,
                      maxLines: 3,
                      decoration: InputDecoration(
                        labelText: localization.groupDescription,
                        hintText: localization.enterGroupDescription,
                        filled: true,
                        fillColor: colors.backgroundContainer,
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: colors.strokeColor,
                            width: 1,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: colors.primary,
                            width: 1,
                          ),
                        ),
                      ),
                      style: textStyles.body.copyWith(
                        color: colors.primaryText,
                      ),
                    ),

                    Container(height: 16),

                    // Selected members list title
                    Container(
                      margin: const EdgeInsetsDirectional.fromSTEB(0, 16, 0, 8),
                      alignment: Alignment.centerLeft,
                      child: Text(
                        'MEMBERS • ${widget.selectedEmployees.length} OF ${widget.selectedEmployees.length}',
                        style: textStyles.body3.copyWith(
                          color: colors.tertiaryText,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),

                    // Selected members list
                    Expanded(
                      child: Container(
                        margin: const EdgeInsetsDirectional.symmetric(
                            horizontal: 0),
                        child: ListView.builder(
                          itemCount: widget.selectedEmployees.length,
                          itemBuilder: (context, index) {
                            final employee = widget.selectedEmployees[index];

                            return Container(
                              margin: const EdgeInsetsDirectional.only(
                                  bottom: 16, top: 12),
                              child: Row(
                                children: [
                                  // Profile image
                                  Container(
                                    width: 40,
                                    height: 40,
                                    child: ClipOval(
                                      child: Image.asset(
                                        employee['profileImage'],
                                        width: 40,
                                        height: 40,
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) {
                                          // Fallback to initials if image fails to load
                                          return Container(
                                            color:
                                                colors.primary.withOpacity(0.1),
                                            child: Center(
                                              child: Text(
                                                employee['name'].isNotEmpty
                                                    ? employee['name'][0]
                                                        .toUpperCase()
                                                    : 'U',
                                                style: textStyles.headline4
                                                    .copyWith(
                                                  color: colors.primary,
                                                ),
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ),

                                  // Name
                                  Expanded(
                                    child: Container(
                                      margin: const EdgeInsetsDirectional.only(
                                          start: 16),
                                      child: Text(
                                        employee['name'],
                                        style: textStyles.headline4.copyWith(
                                          color: colors.primaryText,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Create button
            Container(
              padding: const EdgeInsetsDirectional.all(16),
              child: GestureDetector(
                onTap: _createGroup,
                child: Text(
                  localization.create,
                  style: textStyles.textButton.copyWith(
                    color: _groupNameController.text.trim().isNotEmpty
                        ? colors.primary
                        : colors.secondaryText,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/labels.dart';

/// Location sharing modal component for chat attachments
/// Displays a bottom modal with map, live location option, and nearby places
class LocationSharingModal extends StatefulWidget {
  final Function(String locationType, Map<String, dynamic> locationData)
      onLocationSelected;

  const LocationSharingModal({
    super.key,
    required this.onLocationSelected,
  });

  @override
  State<LocationSharingModal> createState() => _LocationSharingModalState();
}

class _LocationSharingModalState extends State<LocationSharingModal> {
  /// Handle live location sharing
  void _handleLiveLocationShare() {
    widget.onLocationSelected('live_location', {
      'type': 'live',
      'duration': '8 hours', // Default live location duration
    });
    Navigator.pop(context);
  }

  /// Handle current location sharing
  void _handleCurrentLocationShare() {
    widget.onLocationSelected('current_location', {
      'type': 'current',
      'accuracy': '14 metres',
      'latitude': 33.8938, // Sample coordinates (Baghdad)
      'longitude': 44.3661,
    });
    Navigator.pop(context);
  }

  /// Handle close button press
  void _handleClose() {
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final mediaQuery = MediaQuery.of(context);

    return Container(
      height: mediaQuery.size.height -
          mediaQuery.padding.top -
          50, // Account for status bar + extra spacing
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          // Top handle indicator
          Container(
            margin: const EdgeInsetsDirectional.only(top: 8, bottom: 8),
            width: 150,
            height: 4,
            decoration: BoxDecoration(
              color: colors.strokeColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Map/Location Image Container
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            height: 250,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: colors.strokeColor,
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                children: [
                  // Map image
                  Container(
                    width: double.infinity,
                    height: double.infinity,
                    decoration: BoxDecoration(
                      color: colors.background,
                    ),
                    child: Image.asset(
                      'assets/images/location_dark.png',
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        // Fallback if image fails to load
                        return Container(
                          color: Colors.grey[800],
                          child: Icon(
                            Icons.map,
                            color: colors.primary,
                            size: 60,
                          ),
                        );
                      },
                    ),
                  ),

                  // User location pin in center
                  Center(
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: colors.background,
                          width: 2,
                        ),
                      ),
                      child: ClipOval(
                        child: Image.asset(
                          'assets/images/person.png',
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: colors.primary,
                              child: Icon(
                                Icons.person,
                                color: colors.background,
                                size: 20,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Send Your Live Location Container
          Container(
            margin: const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 0),
            width: double.infinity,
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(12),
                onTap: _handleLiveLocationShare,
                child: Container(
                  padding: const EdgeInsetsDirectional.symmetric(
                      horizontal: 16, vertical: 14),
                  decoration: BoxDecoration(
                    color: colors.background,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colors.primaryVariant,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    Labels.sendYourLiveLocation,
                    style: textStyles.body2.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Nearby Places Section
          Container(
            margin: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
            width: double.infinity,
            child: Text(
              Labels.nearbyPlaces,
              style: textStyles.headline4.copyWith(
                color: colors.secondaryText,
              ),
            ),
          ),

          // Send Your Current Location Container
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            width: double.infinity,
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(12),
                onTap: _handleCurrentLocationShare,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                  decoration: BoxDecoration(
                    color: colors.background,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colors.primaryVariant,
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Main text
                      Text(
                        Labels.sendYourCurrentLocation,
                        style: textStyles.body2.copyWith(
                          color: colors.secondaryText,
                        ),
                      ),

                      // Subtitle text
                      Container(
                        margin: const EdgeInsets.only(top: 8),
                        child: Text(
                          Labels.approximateDistance,
                          style: textStyles.body3.copyWith(
                            color: colors.tertiaryText,
                            fontSize: 10,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Spacer to push close button to bottom
          const Expanded(child: SizedBox()),

          // Close button
          Container(
            margin: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 32),
            width: double.infinity,
            height: 56,
            child: OutlinedButton(
              onPressed: _handleClose,
              style: OutlinedButton.styleFrom(
                side: BorderSide(
                  color: colors.tertiaryText,
                  width: 1,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                backgroundColor: Colors.transparent,
              ),
              child: Text(
                Labels.close,
                style: textStyles.body.copyWith(
                  color: colors.secondaryText,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

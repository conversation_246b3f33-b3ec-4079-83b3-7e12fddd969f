import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/labels.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

/// Voice recording widget that shows recording interface
/// Displays animated waveform during recording
class VoiceRecordingWidget extends StatefulWidget {
  final Function(String audioPath, Duration duration, List<double> waveformData)
      onRecordingComplete;
  final VoidCallback onCancel;

  const VoiceRecordingWidget({
    super.key,
    required this.onRecordingComplete,
    required this.onCancel,
  });

  @override
  State<VoiceRecordingWidget> createState() => _VoiceRecordingWidgetState();
}

class _VoiceRecordingWidgetState extends State<VoiceRecordingWidget>
    with TickerProviderStateMixin {
  late FlutterSoundRecorder _audioRecorder;
  bool _isRecording = false;
  Duration _recordingDuration = Duration.zero;
  Timer? _timer;
  late AnimationController _pulseController;
  late AnimationController _waveController;
  List<double> _waveformData = [];
  String? _audioPath;

  @override
  void initState() {
    super.initState();
    _audioRecorder = FlutterSoundRecorder();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _waveController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    _initializeRecording();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pulseController.dispose();
    _waveController.dispose();
    _audioRecorder.closeRecorder();
    super.dispose();
  }

  Future<void> _initializeRecording() async {
    try {
      // Initialize the recorder
      await _audioRecorder.openRecorder();

      // Request microphone permission
      final permission = await Permission.microphone.request();
      if (permission.isGranted) {
        await _startRecording();
      } else {
        // Handle permission denied
        widget.onCancel();
      }
    } catch (e) {
      widget.onCancel();
    }
  }

  Future<void> _startRecording() async {
    try {
      // Get temporary directory for audio file
      final directory = await getTemporaryDirectory();
      final fileName =
          'voice_message_${DateTime.now().millisecondsSinceEpoch}.aac';
      _audioPath = '${directory.path}/$fileName';

      // Start recording
      await _audioRecorder.startRecorder(
        toFile: _audioPath,
        codec: Codec.aacADTS,
      );

      setState(() {
        _isRecording = true;
      });

      // Start animations
      _pulseController.repeat();
      _waveController.repeat();

      // Start timer
      _timer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
        setState(() {
          _recordingDuration = Duration(milliseconds: timer.tick * 100);
        });

        // Generate random waveform data for animation
        _generateWaveformData();
      });
    } catch (e) {
      // Handle recording error
      widget.onCancel();
    }
  }

  void _generateWaveformData() {
    // Generate random waveform data for visual effect
    // In a real app, you would get actual audio levels
    final random = math.Random();
    if (_waveformData.length < 50) {
      _waveformData.add(0.2 + random.nextDouble() * 0.8);
    } else {
      // Shift existing data and add new point
      _waveformData.removeAt(0);
      _waveformData.add(0.2 + random.nextDouble() * 0.8);
    }
  }

  Future<void> _stopRecording() async {
    try {
      final path = await _audioRecorder.stopRecorder();
      _timer?.cancel();
      _pulseController.stop();
      _waveController.stop();

      setState(() {
        _isRecording = false;
      });

      if (path != null && _audioPath != null) {
        // Return the recorded audio
        widget.onRecordingComplete(
            _audioPath!, _recordingDuration, _waveformData);
      } else {
        widget.onCancel();
      }
    } catch (e) {
      widget.onCancel();
    }
  }

  void _cancelRecording() async {
    try {
      await _audioRecorder.stopRecorder();
      _timer?.cancel();
      _pulseController.stop();
      _waveController.stop();

      // Delete the recorded file
      if (_audioPath != null) {
        final file = File(_audioPath!);
        if (await file.exists()) {
          await file.delete();
        }
      }
    } catch (e) {
      // Handle error
    }

    widget.onCancel();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colors.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: colors.strokeColor.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(bottom: 20),
            decoration: BoxDecoration(
              color: colors.strokeColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Recording indicator
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AnimatedBuilder(
                animation: _pulseController,
                builder: (context, child) {
                  return Container(
                    width: 12 + (_pulseController.value * 4),
                    height: 12 + (_pulseController.value * 4),
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                  );
                },
              ),
              const SizedBox(width: 8),
              Text(
                Labels.recording,
                style: textStyles.body2.copyWith(
                  color: colors.primaryText,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Waveform visualization
          Container(
            height: 60,
            margin: const EdgeInsets.symmetric(horizontal: 20),
            child: AnimatedBuilder(
              animation: _waveController,
              builder: (context, child) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: List.generate(
                    math.min(_waveformData.length, 40),
                    (index) {
                      final animationOffset = (index * 0.1) % 1.0;
                      final animationValue = 0.3 +
                          0.7 *
                              (0.5 +
                                  0.5 *
                                      math.sin((_waveController.value *
                                              2 *
                                              math.pi) +
                                          animationOffset * 2 * math.pi));

                      return Container(
                        width: 3,
                        height: (_waveformData.length > index
                                ? _waveformData[index]
                                : 0.3) *
                            60 *
                            animationValue,
                        decoration: BoxDecoration(
                          color: colors.primary,
                          borderRadius: BorderRadius.circular(1.5),
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 20),

          // Duration
          Text(
            _formatDuration(_recordingDuration),
            style: textStyles.headline3.copyWith(
              color: colors.primaryText,
            ),
          ),

          const SizedBox(height: 30),

          // Control buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Cancel button
              Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(30),
                  onTap: _cancelRecording,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: colors.error.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      SolarIconsOutline.closeCircle,
                      size: 28,
                      color: colors.error,
                    ),
                  ),
                ),
              ),

              // Send button
              Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(30),
                  onTap: _stopRecording,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: colors.primary,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.send,
                      size: 28,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }
}

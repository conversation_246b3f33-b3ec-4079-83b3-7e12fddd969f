import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:solar_icons/solar_icons.dart';

/// Chat input field component for sending messages
/// Includes text input, attachment, and voice message buttons
class ChatInputField extends StatefulWidget {
  final Function(String) onSendMessage;
  final VoidCallback onAttachmentPressed;
  final VoidCallback onVoicePressed;

  const ChatInputField({
    super.key,
    required this.onSendMessage,
    required this.onAttachmentPressed,
    required this.onVoicePressed,
  });

  @override
  State<ChatInputField> createState() => _ChatInputFieldState();
}

class _ChatInputFieldState extends State<ChatInputField> {
  final TextEditingController _messageController = TextEditingController();
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _messageController.addListener(() {
      setState(() {
        _hasText = _messageController.text.trim().isNotEmpty;
      });
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  void _sendMessage() {
    final message = _messageController.text.trim();
    if (message.isNotEmpty) {
      widget.onSendMessage(message);
      _messageController.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      width: MediaQuery.of(context).size.width *
          0.96, // Slightly less width than other components
      decoration: BoxDecoration(
        color: colors.background,
        // border: Border(
        //   top: BorderSide(
        //     color: colors.strokeColor.withOpacity(0.1),
        //     width: 1,
        //   ),
        // ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: SafeArea(
        top: false,
        child: Container(
          decoration: BoxDecoration(
            color: colors.backgroundContainer,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: colors.strokeColor,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              // Attachment button inside container
              Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(25),
                  onTap: widget.onAttachmentPressed,
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    child: Icon(
                      SolarIconsOutline.paperclip,
                      size: 22,
                      color: colors.tertiaryText,
                    ),
                  ),
                ),
              ),

              // Text field
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 14,
                  ),
                  child: TextField(
                    controller: _messageController,
                    style: textStyles.body2.copyWith(
                      color: colors.primaryText,
                    ),
                    decoration: InputDecoration(
                      hintText: localization.writeAMessage,
                      hintStyle: textStyles.body2.copyWith(
                        color: colors.tertiaryText,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                      isDense: true,
                    ),
                    maxLines: 4,
                    minLines: 1,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
              ),

              // Send button (shown when there's text) or voice button inside container
              Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(25),
                  onTap: _hasText ? _sendMessage : widget.onVoicePressed,
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    child: _hasText
                        ? Icon(
                            Icons.send,
                            size: 24,
                            color: colors.primary,
                          )
                        : Icon(
                            SolarIconsOutline.microphone,
                            size: 24,
                            color: colors.primary,
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

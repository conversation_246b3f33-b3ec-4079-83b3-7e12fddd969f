import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:audioplayers/audioplayers.dart';

/// Voice message bubble component for displaying voice messages
/// Shows waveform, play/pause button, and duration
class VoiceMessageBubble extends StatefulWidget {
  final String audioPath;
  final String time;
  final bool isSentByMe;
  final bool isRead;
  final Duration duration;
  final List<double> waveformData;

  const VoiceMessageBubble({
    super.key,
    required this.audioPath,
    required this.time,
    required this.isSentByMe,
    required this.isRead,
    required this.duration,
    required this.waveformData,
  });

  @override
  State<VoiceMessageBubble> createState() => _VoiceMessageBubbleState();
}

class _VoiceMessageBubbleState extends State<VoiceMessageBubble>
    with TickerProviderStateMixin {
  late AudioPlayer _audioPlayer;
  bool _isPlaying = false;
  Duration _currentPosition = Duration.zero;
  late AnimationController _waveAnimationController;

  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
    _waveAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Listen to player state changes
    _audioPlayer.onPlayerStateChanged.listen((PlayerState state) {
      if (mounted) {
        setState(() {
          _isPlaying = state == PlayerState.playing;
        });

        if (_isPlaying) {
          _waveAnimationController.repeat();
        } else {
          _waveAnimationController.stop();
        }
      }
    });

    // Listen to position changes
    _audioPlayer.onPositionChanged.listen((Duration position) {
      if (mounted) {
        setState(() {
          _currentPosition = position;
        });
      }
    });

    // Listen to completion
    _audioPlayer.onPlayerComplete.listen((_) {
      if (mounted) {
        setState(() {
          _currentPosition = Duration.zero;
          _isPlaying = false;
        });
        _waveAnimationController.stop();
      }
    });
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    _waveAnimationController.dispose();
    super.dispose();
  }

  Future<void> _togglePlayPause() async {
    try {
      if (_isPlaying) {
        await _audioPlayer.pause();
      } else {
        await _audioPlayer.play(DeviceFileSource(widget.audioPath));
      }
    } catch (e) {
      print('Error playing audio: $e');
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Container(
      margin: EdgeInsets.only(
        left: widget.isSentByMe ? 50 : 0,
        right: widget.isSentByMe ? 0 : 50,
        bottom: 8,
      ),
      child: Row(
        mainAxisAlignment:
            widget.isSentByMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // Profile image for received messages
          if (!widget.isSentByMe)
            Container(
              margin: const EdgeInsets.only(right: 8, bottom: 4),
              child: CircleAvatar(
                radius: 16,
                backgroundColor: colors.strokeColor,
                child: Icon(
                  Icons.person,
                  size: 20,
                  color: colors.tertiaryText,
                ),
              ),
            ),

          // Voice message bubble
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.75,
                minWidth: 200,
              ),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: widget.isSentByMe ? colors.primary : colors.surface,
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(16),
                  topRight: const Radius.circular(16),
                  bottomLeft: Radius.circular(widget.isSentByMe ? 16 : 4),
                  bottomRight: Radius.circular(widget.isSentByMe ? 4 : 16),
                ),
                boxShadow: [
                  BoxShadow(
                    color: colors.strokeColor.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Voice message content
                  Row(
                    children: [
                      // Play/Pause button
                      Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(20),
                          onTap: _togglePlayPause,
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            child: Icon(
                              _isPlaying
                                  ? SolarIconsBold.pause
                                  : SolarIconsBold.play,
                              size: 24,
                              color: widget.isSentByMe
                                  ? Colors.white
                                  : colors.primary,
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(width: 8),

                      // Waveform
                      Expanded(
                        child: _buildWaveform(colors),
                      ),

                      const SizedBox(width: 8),

                      // Duration
                      Text(
                        _formatDuration(
                          _isPlaying ? _currentPosition : widget.duration,
                        ),
                        style: textStyles.body3.copyWith(
                          color: widget.isSentByMe
                              ? Colors.white.withOpacity(0.8)
                              : colors.tertiaryText,
                        ),
                      ),
                    ],
                  ),

                  // Time and read status
                  Container(
                    margin: const EdgeInsets.only(top: 4),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          widget.time,
                          style: textStyles.body3.copyWith(
                            color: widget.isSentByMe
                                ? Colors.white.withOpacity(0.7)
                                : colors.tertiaryText,
                            fontSize: 11,
                          ),
                        ),
                        if (widget.isSentByMe) ...[
                          const SizedBox(width: 4),
                          Icon(
                            widget.isRead ? Icons.done_all : Icons.done,
                            size: 14,
                            color: widget.isRead
                                ? colors.primary
                                : Colors.white.withOpacity(0.7),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWaveform(AppColors colors) {
    return AnimatedBuilder(
      animation: _waveAnimationController,
      builder: (context, child) {
        return SizedBox(
          height: 40,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: List.generate(widget.waveformData.length, (index) {
              final progress = _currentPosition.inMilliseconds /
                  widget.duration.inMilliseconds;
              final isActive = (index / widget.waveformData.length) <= progress;

              // Animation effect for playing state
              double animationValue = 1.0;
              if (_isPlaying) {
                final animationOffset = (index * 0.1) % 1.0;
                animationValue = 0.3 +
                    0.7 *
                        (0.5 +
                            0.5 *
                                math.sin((_waveAnimationController.value *
                                        2 *
                                        math.pi) +
                                    animationOffset * 2 * math.pi));
              }

              return Container(
                width: 3,
                height: widget.waveformData[index] * 40 * animationValue,
                decoration: BoxDecoration(
                  color: isActive
                      ? (widget.isSentByMe ? Colors.white : colors.primary)
                      : (widget.isSentByMe
                          ? Colors.white.withOpacity(0.4)
                          : colors.primary.withOpacity(0.3)),
                  borderRadius: BorderRadius.circular(1.5),
                ),
              );
            }),
          ),
        );
      },
    );
  }
}

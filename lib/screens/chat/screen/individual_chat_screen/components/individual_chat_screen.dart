import 'dart:io';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/image/image_service.dart';
import 'package:ako_basma/util/document/document_service.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/chat_header.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/chat_messages_list.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/chat_input_field.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/attachment_menu.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/contact_selection_modal.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/location_sharing_modal.dart';

class IndividualChatScreen extends StatefulWidget {
  final String userName;
  final String userImage;
  final String lastSeen;
  final bool isOnline;

  const IndividualChatScreen({
    super.key,
    required this.userName,
    required this.userImage,
    required this.lastSeen,
    this.isOnline = false,
  });

  @override
  State<IndividualChatScreen> createState() => _IndividualChatScreenState();
}

class _IndividualChatScreenState extends State<IndividualChatScreen> {
  bool _showAttachmentMenu = false;

  /// Toggles the attachment menu visibility
  void _toggleAttachmentMenu() {
    setState(() {
      _showAttachmentMenu = !_showAttachmentMenu;
    });
  }

  /// Handles camera functionality - opens camera to capture photo
  Future<void> _handleCameraPress() async {
    try {
      _toggleAttachmentMenu(); // Close attachment menu first

      // Use the reusable image service to capture photo
      final File? photo = await ImageService.captureFromCamera();

      if (photo != null) {
        // Handle the captured photo

        // TODO: Process and send the image in chat
        // implement image sending logic here

        // Show success feedback using image service
        if (mounted) {
          ImageService.showSuccessMessage(
              context, 'Photo captured successfully!');
        }
      }
    } catch (e) {
      // Handle any errors during camera operation using image service
      if (mounted) {
        ImageService.showErrorMessage(
            context, 'Failed to capture photo: ${e.toString()}');
      }
    }
  }

  /// Handles gallery functionality - opens gallery to select photo
  Future<void> _handleGalleryPress() async {
    try {
      _toggleAttachmentMenu(); // Close attachment menu first

      // Use the reusable image service to select from gallery
      final File? image = await ImageService.selectFromGallery();

      if (image != null) {
        // Handle the selected image
        // TODO: Process and send the image in chat

        // Show success feedback using image service
        if (mounted) {
          ImageService.showSuccessMessage(
              context, 'Image selected successfully!');
        }
      }
    } catch (e) {
      // Handle any errors during gallery operation using image service
      if (mounted) {
        ImageService.showErrorMessage(
            context, 'Failed to select image: ${e.toString()}');
      }
    }
  }

  /// Handles document functionality - opens file picker to select document
  Future<void> _handleDocumentPress() async {
    try {
      _toggleAttachmentMenu(); // Close attachment menu first

      // Use the reusable document service to select document
      final File? document = await DocumentService.selectDocument();

      if (document != null) {
        // Handle the selected document
        final fileName = DocumentService.getFileName(document.path);
        final fileSize = DocumentService.getFileSize(document);
        print('Document selected: ${document.path}');
        print('File name: $fileName, Size: $fileSize');
        // TODO: Process and send the document in chat

        // Show success feedback using document service
        if (mounted) {
          DocumentService.showSuccessMessage(
              context, 'Document "$fileName" selected successfully!');
        }
      }
    } catch (e) {
      // Handle any errors during document operation using document service
      if (mounted) {
        DocumentService.showErrorMessage(
            context, 'Failed to select document: ${e.toString()}');
      }
    }
  }

  /// Handles contact selection functionality - opens contact selection popup
  void _handleContactPress() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) => ContactSelectionModal(
        onContactsSelected: (selectedContacts) {
          // Handle selected contacts
          if (selectedContacts.isNotEmpty) {
            print(
                'Selected contacts: ${selectedContacts.map((c) => c.name).join(', ')}');
            // TODO: Implement contact sharing logic
          }
        },
      ),
    );
  }

  /// Handles location sharing functionality - opens location sharing modal
  void _handleLocationPress() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) => LocationSharingModal(
        onLocationSelected: (locationType, locationData) {
          // Handle location sharing
          print('Location type: $locationType');
          print('Location data: $locationData');
          // TODO: Implement location sharing logic
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;

    return GestureDetector(
      // Close attachment menu when tapping outside
      onTap: () {
        if (_showAttachmentMenu) {
          _toggleAttachmentMenu();
        }
      },
      child: Scaffold(
        backgroundColor: colors.background,
        body: Stack(
          children: [
            // Main chat layout
            Column(
              children: [
                // Chat Header
                ChatHeader(
                  userName: widget.userName,
                  userImage: widget.userImage,
                  lastSeen: widget.lastSeen,
                  isOnline: widget.isOnline,
                  onBackPressed: () => context.pop(),
                ),

                // Messages List
                const Expanded(
                  child: ChatMessagesList(),
                ),

                // Input field at bottom
                ChatInputField(
                  onSendMessage: (message) {
                    // Handle sending message
                    // TODO: Implement message sending logic
                    print('Message sent: $message');
                  },
                  onAttachmentPressed: _toggleAttachmentMenu,
                  onVoicePressed: () {
                    // Handle voice message
                    print('Voice message pressed');
                  },
                ),
              ],
            ),

            // Attachment menu overlay
            if (_showAttachmentMenu)
              Positioned(
                bottom: 100, // Position above the input field
                left: MediaQuery.of(context).size.width *
                    0.02, // Center with same margin as input field
                right: MediaQuery.of(context).size.width * 0.02,
                child: AttachmentMenu(
                  onCameraPressed:
                      _handleCameraPress, // Use the new camera handler
                  onRecordPressed: () {
                    // Handle record action
                    _toggleAttachmentMenu();
                    print('Record pressed');
                  },
                  onContactPressed: () {
                    // Handle contact action
                    _toggleAttachmentMenu();
                    _handleContactPress();
                  },
                  onGalleryPressed:
                      _handleGalleryPress, // Use the new gallery handler
                  onLocationPressed: () {
                    // Handle location action
                    _toggleAttachmentMenu();
                    _handleLocationPress();
                  },
                  onDocumentPressed:
                      _handleDocumentPress, // Use the new document handler
                ),
              ),
          ],
        ),
      ),
    );
  }
}

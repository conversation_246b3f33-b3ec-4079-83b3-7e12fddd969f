import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/message_bubble.dart';

/// Chat messages list component displaying conversation history
/// Shows messages in chronological order with different styles for sent/received
class ChatMessagesList extends StatelessWidget {
  const ChatMessagesList({super.key});

  // Sample chat data - in real app this would come from API/database
  List<Map<String, dynamic>> _getSampleMessages() {
    return [
      {
        'id': '1',
        'message':
            'This is your delivery driver from Speedy Chow. I\'m just around the corner from your place. 😊',
        'time': '10:10',
        'isSentByMe': false,
        'isRead': true,
        'profileImage': 'assets/images/person.png',
      },
      {
        'id': '2',
        'message': 'Hi!',
        'time': '10:10',
        'isSentByMe': true,
        'isRead': true,
        'profileImage': '',
      },
      {
        'id': '3',
        'message':
            'Awesome, thanks for letting me know! Can\'t wait for my delivery. 🎉',
        'time': '10:11',
        'isSentByMe': true,
        'isRead': true,
        'profileImage': '',
      },
      {
        'id': '4',
        'message': 'No problem at all!\nI\'ll be there in about 15 minutes.',
        'time': '10:11',
        'isSentByMe': false,
        'isRead': false,
        'profileImage': 'assets/images/person.png',
      },
      {
        'id': '5',
        'message': 'I\'ll text you when I arrive.',
        'time': '10:11',
        'isSentByMe': false,
        'isRead': false,
        'profileImage': 'assets/images/person.png',
      },
      {
        'id': '6',
        'message': 'Great! 😊',
        'time': '10:12',
        'isSentByMe': true,
        'isRead': true,
        'profileImage': '',
      },
    ];
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final messages = _getSampleMessages();

    return Container(
      color: colors.background,
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        itemCount: messages.length,
        itemBuilder: (context, index) {
          final message = messages[index];

          // Check if we need to show time separator
          bool showTimeHeader = false;
          if (index == 0) {
            showTimeHeader = true;
          } else {
            // You can add logic here to compare dates and show day separators
            // For now, we'll just show it for the first message
          }

          return Column(
            children: [
              // Time separator (optional)
              if (showTimeHeader)
                Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Text(
                    Labels
                        .today, // You can make this dynamic based on message date
                    style: theme.extension<TextStyles>()!.body3.copyWith(
                          color: colors.tertiaryText,
                        ),
                  ),
                ),

              // Message bubble
              MessageBubble(
                message: message['message'],
                time: message['time'],
                isSentByMe: message['isSentByMe'],
                isRead: message['isRead'],
                profileImage: message['profileImage'],
                showAvatar: false, // No avatars needed
              ),

              // Add spacing between messages
              Container(
                margin: const EdgeInsets.only(bottom: 8),
              ),
            ],
          );
        },
      ),
    );
  }
}

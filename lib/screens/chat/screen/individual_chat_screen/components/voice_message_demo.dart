import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/voice_message_bubble.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/voice_recording_widget.dart';

/// Demo screen to showcase voice message UI components
class VoiceMessageDemo extends StatefulWidget {
  const VoiceMessageDemo({super.key});

  @override
  State<VoiceMessageDemo> createState() => _VoiceMessageDemoState();
}

class _VoiceMessageDemoState extends State<VoiceMessageDemo> {
  bool _showRecording = false;

  // Sample waveform data
  final List<double> _sampleWaveform = [
    0.3,
    0.7,
    0.5,
    0.9,
    0.4,
    0.8,
    0.6,
    0.3,
    0.7,
    0.5,
    0.9,
    0.4,
    0.8,
    0.6,
    0.3,
    0.7,
    0.5,
    0.9,
    0.4,
    0.8,
    0.6,
    0.3,
    0.7,
    0.5,
    0.9,
    0.4,
    0.8,
    0.6,
    0.3,
    0.7,
    0.5,
    0.9,
    0.4,
    0.8,
    0.6,
    0.3,
    0.7,
    0.5,
    0.9,
    0.4
  ];

  void _showRecordingWidget() {
    setState(() {
      _showRecording = true;
    });
  }

  void _hideRecordingWidget() {
    setState(() {
      _showRecording = false;
    });
  }

  void _handleRecordingComplete(
      String audioPath, Duration duration, List<double> waveformData) {
    _hideRecordingWidget();

    // Show a snackbar to indicate recording was completed
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Voice message recorded: ${duration.inSeconds}s'),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Scaffold(
      backgroundColor: colors.background,
      appBar: AppBar(
        title: Text(
          'Voice Message Demo',
          style: textStyles.headline2.copyWith(color: colors.primaryText),
        ),
        backgroundColor: colors.surface,
        elevation: 0,
        iconTheme: IconThemeData(color: colors.primaryText),
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                Text(
                  'Voice Message Components',
                  style:
                      textStyles.headline3.copyWith(color: colors.primaryText),
                ),

                const SizedBox(height: 20),

                // Description
                Text(
                  'This demo shows the voice message UI components that have been implemented:',
                  style: textStyles.body2.copyWith(color: colors.secondaryText),
                ),

                const SizedBox(height: 30),

                // Sent Voice Message Example
                Text(
                  'Sent Voice Message:',
                  style: textStyles.body.copyWith(
                    color: colors.primaryText,
                    fontWeight: FontWeight.w600,
                  ),
                ),

                const SizedBox(height: 10),

                VoiceMessageBubble(
                  audioPath: '/demo/path/voice1.aac',
                  time: '10:13',
                  isSentByMe: true,
                  isRead: true,
                  duration: const Duration(minutes: 2, seconds: 20),
                  waveformData: _sampleWaveform,
                ),

                const SizedBox(height: 30),

                // Received Voice Message Example
                Text(
                  'Received Voice Message:',
                  style: textStyles.body.copyWith(
                    color: colors.primaryText,
                    fontWeight: FontWeight.w600,
                  ),
                ),

                const SizedBox(height: 10),

                VoiceMessageBubble(
                  audioPath: '/demo/path/voice2.aac',
                  time: '10:14',
                  isSentByMe: false,
                  isRead: false,
                  duration: const Duration(minutes: 1, seconds: 25),
                  waveformData: _sampleWaveform.reversed.toList(),
                ),

                const SizedBox(height: 40),

                // Record Button
                Center(
                  child: ElevatedButton.icon(
                    onPressed: _showRecordingWidget,
                    icon: const Icon(Icons.mic, color: Colors.white),
                    label: Text(
                      'Test Voice Recording',
                      style: textStyles.body.copyWith(color: Colors.white),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colors.primary,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 20),

                // Features List
                Text(
                  'Features Implemented:',
                  style: textStyles.body.copyWith(
                    color: colors.primaryText,
                    fontWeight: FontWeight.w600,
                  ),
                ),

                const SizedBox(height: 10),

                ...const [
                  '• Animated waveform visualization',
                  '• Play/pause audio controls',
                  '• Duration display and progress tracking',
                  '• Different styling for sent vs received messages',
                  '• Real-time recording interface',
                  '• Voice recording with waveform animation',
                  '• Integration with existing chat system',
                  '• Theme-consistent design',
                ].map((feature) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Text(
                        feature,
                        style: textStyles.body2
                            .copyWith(color: colors.secondaryText),
                      ),
                    )),

                const SizedBox(height: 100), // Extra space for recording widget
              ],
            ),
          ),

          // Voice recording widget overlay
          if (_showRecording)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: VoiceRecordingWidget(
                onRecordingComplete: _handleRecordingComplete,
                onCancel: _hideRecordingWidget,
              ),
            ),
        ],
      ),
    );
  }
}

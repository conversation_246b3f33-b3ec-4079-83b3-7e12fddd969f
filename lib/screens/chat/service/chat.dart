import 'package:ako_basma/screens/chat/constant.dart';
import 'package:dio/dio.dart';
import 'package:fluttertoast/fluttertoast.dart';

class ChatService {
  final dio = Dio();

  final headers = {'Content-Type': 'application/json'};

  Future<String?> sendMessage(String text) async {
    try {
      final response = await dio.request(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=$apiKey',
        options: Options(
          method: 'POST',
          headers: headers,
          receiveTimeout: Duration(seconds: 30),
        ),
        data: {
          "contents": [
            {
              "parts": [
                {"text": text}
              ]
            }
          ]
        },
      );

      if (response.statusCode == 200) {
        return response.data['candidates'][0]['content']['parts'][0]['text'];
      } else {
        Fluttertoast.showToast(
            msg: "Some error here. Code : ${response.statusCode.toString()}",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.CENTER,
            timeInSecForIosWeb: 1,
            fontSize: 16.0);
        print(response.statusMessage);
        return 'Some error here. Code : ${response.statusCode.toString()}';
      }
    } catch (e) {
      Fluttertoast.showToast(
          msg: "Some error here. Code : ${e.toString()}",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          fontSize: 16.0);
      print(e);
      return 'Some error here. Code : ${e.toString()}';
    }
  }
}

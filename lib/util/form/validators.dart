import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class Validators {
  static String? required(String? value, [BuildContext? context]) {
    if ((value?.trim() ?? '').isEmpty) {
      return 'This is required';
    }
    return null;
  }

  static String? phone(String? value, {bool required = true}) {
    if ((value?.trim() ?? '').isEmpty) {
      return required ? 'This is required' : null;
    }
    bool isValidPhoneNumber(String? value) =>
        RegExp(r'(^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$)')
            .hasMatch(value ?? '');

    return isValidPhoneNumber(value) ? null : 'Phone number looks incorrect';
  }

  static get phoneInputFormatter =>
      FilteringTextInputFormatter.allow(RegExp(r'[+\d]'));
  static get digitsInputFormatter =>
      FilteringTextInputFormatter.allow(RegExp(r'[\d]'));
}

import 'dart:convert';

import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../../model/profile.dart';

class HiveUtils {
  static const accBoxKey = 'appAccBox';
  static const tokenKey = 'appToken';
  static const profileKey = 'profile';
  static const lastLocationKey = 'lastLocation';
  static const useNotifications = 'useNotifications';
  // static const lastUnsuccessfulPayment = 'lastUnsuccessfulPayment';

  static const promoShownFlag = 'promoShownFlag';

  static Future<void> setToken(String? token) async {
    await Hive.box(HiveUtils.accBoxKey).put(HiveUtils.tokenKey, token);
  }

  static Future<void> setProfile(Profile? profile) async {
    await Hive.box(HiveUtils.accBoxKey).put(HiveUtils.profileKey, profile);
  }

  static Future<void> setPromoFlag() async {
    await Hive.box(HiveUtils.accBoxKey).put(HiveUtils.promoShownFlag, true);
  }

  static Future<void> setNotificationPref(bool? value) async {
    await Hive.box(HiveUtils.accBoxKey).put(HiveUtils.useNotifications, value);
  }

  static Future<void> setLastLocation(LatLng latlng) async {
    await Hive.box(HiveUtils.accBoxKey)
        .put(HiveUtils.lastLocationKey, latlng.toJson());
  }

  // static Future<void> setLastUnsuccessfulPaymentData({dynamic payload}) async {
  //   final data = (payload == null)
  //       ? null
  //       : jsonEncode({
  //           'data': payload,
  //           'timestamp': DateTime.now().toString(),
  //         });
  //   await Hive.box(HiveUtils.accBoxKey)
  //       .put(HiveUtils.lastUnsuccessfulPayment, data);
  // }

  static Future<void> cleanUp() async {
    await Hive.box(HiveUtils.accBoxKey).clear();
  }

  static String? token() {
    return Hive.box(HiveUtils.accBoxKey).get(HiveUtils.tokenKey);
  }

  static Profile? profile() {
    return Hive.box(HiveUtils.accBoxKey).get(HiveUtils.profileKey);
  }

  static bool? promoFlag() {
    return Hive.box(HiveUtils.accBoxKey).get(HiveUtils.promoShownFlag);
  }

  static bool? notficationPreference() {
    return Hive.box(HiveUtils.accBoxKey).get(HiveUtils.useNotifications);
  }

  // static dynamic lastUnsuccessfulPaymentData() {
  //   final data = Hive.box(HiveUtils.accBoxKey)
  //       .get(HiveUtils.lastUnsuccessfulPayment) as String?;
  //   try {
  //     return data == null ? null : jsonDecode(data);
  //   } catch (e) {
  //     return null;
  //   }
  // }

  static LatLng? lastLocation() {
    final obj = Hive.box(HiveUtils.accBoxKey).get(HiveUtils.lastLocationKey);
    return (obj == null) ? null : LatLng.fromJson(obj);
  }
}

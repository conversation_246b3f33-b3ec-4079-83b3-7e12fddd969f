import 'dart:ui';
import 'package:flutter/foundation.dart';

import 'package:dio/dio.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../util/hive/hive_util.dart';
import '../../components/form/location_selector.dart';
import '../../providers/api/keys.dart';

/// For location related stuff.
/// Geocoding, Places api, Geolocator, etc.
/// Enable appropriate apis and update api keys in constants.
/// Configure geolocator & locations permission handler also.

final dio = Dio();

/// search for locations with a text
/// uses places/textSearch v2
Future<List<LocationId>> searchLocation(
  String query, {
  CancelToken? cancelToken,
}) async {
  final response = await dio.get(
    'https://maps.googleapis.com/maps/api/place/textsearch/json',
    queryParameters: {
      "query": query,
      'key': mapsKey,
    },
    options: Options(
      preserveHeaderCase: true,
      headers: {
        'Content-Type': 'application/json',
        'X-Goog-Api-Key': mapsKey,
        'X-Goog-FieldMask':
            'places.displayName,places.formattedAddress,places.location',
      },
    ),
    cancelToken: cancelToken,
  );

  if (response.statusCode == 200) {
    final data = response.data;
    if (data['status'] == 'OK' && data['results'].isNotEmpty) {
      final place = (data['results'] as List).map((e) => LocationId(
            label: e['name'],
            address: e['formatted_address'],
            hasAddressPlaceholder:
                ((e['formatted_address'] as String?) ?? "").trim().isEmpty,
            latlng: LatLng(
              e['geometry']['location']['lat'],
              e['geometry']['location']['lng'],
            ),
          )); // Assuming you want the first result
      return place.toList();
      // Extract details directly from the Text Search response
    } else {
      print('No places found matching the search query');
      return [];
    }
  } else {
    // print('Text Search API error:', data);
    // return [];
    throw ('Error while fetching search results!');
  }
}

/// Geocoding function, to get address for latlng.
/// uses google geocode
Future<String?> getAddress(LatLng latLng, Locale? locale) async {
  final response = await dio
      .get('https://maps.google.com/maps/api/geocode/json', queryParameters: {
    'key': mapsKey,
    if (locale != null) 'language': locale.languageCode,
    'latlng': '${latLng.latitude},${latLng.longitude}'
  });

  if (response.statusCode == 200) {
    String formattedAddress = response.data["results"][0]["formatted_address"];
    print("response ==== $formattedAddress");
    return formattedAddress;
  }
  return null;
}

/// Obtains device location,
/// uses geolocator
Future<LatLng?> getCurrentLocation() async {
  bool serviceEnabled;
  LocationPermission permission;

  // Test if location services are enabled.
  serviceEnabled = await Geolocator.isLocationServiceEnabled();
  if (!serviceEnabled) {
    // Location services are not enabled don't continue
    // accessing the position and request users of the
    // App to enable the location services.
    return Future.error('Location services are disabled.');
  }

  permission = await Geolocator.checkPermission();
  if (permission == LocationPermission.denied) {
    permission = await Geolocator.requestPermission();
    if (permission == LocationPermission.denied) {
      // Permissions are denied, next time you could try
      // requesting permissions again (this is also where
      // Android's shouldShowRequestPermissionRationale
      // returned true. According to Android guidelines
      // your App should show an explanatory UI now.
      return Future.error('Location permissions are denied');
    }
  }

  if (permission == LocationPermission.deniedForever) {
    // Permissions are denied forever, handle appropriately.
    return Future.error(
        'Location permissions are permanently denied, we cannot request permissions.');
  }

  // When we reach here, permissions are granted and we can
  // continue accessing the position of the device.
  try {
    final pos = await Geolocator.getCurrentPosition();
    final loc = LatLng(pos.latitude, pos.longitude);
    HiveUtils.setLastLocation(loc);
    return loc;
  } catch (e) {
    if (kDebugMode) {
      print('Error in gewtting current location $e');
    }
    return null;
  }
}

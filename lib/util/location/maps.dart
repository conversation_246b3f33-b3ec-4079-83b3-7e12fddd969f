/// this file is for map launcher. intent for launching gmap/ apple map app.

// import 'dart:io';

// import 'package:flutter/foundation.dart';
// import 'package:map_launcher/map_launcher.dart' as map;
// import 'package:google_maps_flutter/google_maps_flutter.dart';

// void startNavigation(LatLng? latLng) {
//   const local = LatLng(
//     28.612912,
//     77.2114853,
//   );
//   const sf = LatLng(37.7434817, -122.451872);
//   const us = LatLng(37.3735964, 164.1195279);

//   launchMapNavigator(latLng ?? sf);
// }

// void launchMapNavigator(LatLng latLng) async {
//   final mapMode = Platform.isAndroid
//       ? map.MapType.google
//       : (await map.MapLauncher.isMapAvailable(map.MapType.google) ?? false)
//           ? map.MapType.google
//           : map.MapType.apple;
//   if (kDebugMode) {
//     print('attempt to start $mapMode');
//   }
//   await map.MapLauncher.showDirections(
//     mapType: mapMode,
//     destination: map.Coords(
//       latLng.latitude,
//       latLng.longitude,
//     ),
//     directionsMode: map.DirectionsMode.driving,
//     extraParams: {
//       // if (mapMode == MapType.google) 'nav': '1',
//       if (mapMode == map.MapType.google) 'dir_action': 'navigate',
//     },
//   );
// }

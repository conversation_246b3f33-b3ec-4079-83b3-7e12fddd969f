import 'dart:io';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/labels.dart';

/// Service class for handling document operations (file picking)
/// Follows the app's coding standards for reusability and clean code
class DocumentService {
  /// Supported document file extensions
  static const List<String> supportedExtensions = [
    'pdf',
    'doc',
    'docx',
    'txt',
    'xls',
    'xlsx',
    'ppt',
    'pptx',
    'zip',
    'rar'
  ];

  /// Opens file picker to select a document
  /// Returns the selected file or null if operation was cancelled
  static Future<File?> selectDocument({
    List<String>? allowedExtensions,
    String? dialogTitle,
  }) async {
    try {
      // Check and request storage permission
      final storagePermission = await Permission.storage.request();
      if (storagePermission.isDenied || storagePermission.isPermanentlyDenied) {
        // For Android 13+ try with manage external storage permission
        final manageStoragePermission =
            await Permission.manageExternalStorage.request();
        if (manageStoragePermission.isDenied ||
            manageStoragePermission.isPermanentlyDenied) {
          throw Exception(
              'Storage permission is required to access documents. Please grant permission in app settings.');
        }
      }

      // Select document from device
      final FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: allowedExtensions ?? supportedExtensions,
        dialogTitle: dialogTitle ?? Labels.selectDocument,
        allowMultiple: false,
      );

      if (result != null &&
          result.files.isNotEmpty &&
          result.files.single.path != null) {
        return File(result.files.single.path!);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to select document: ${e.toString()}');
    }
  }

  /// Selects multiple documents from device
  /// Returns list of selected files or empty list if operation was cancelled
  static Future<List<File>> selectMultipleDocuments({
    List<String>? allowedExtensions,
    String? dialogTitle,
  }) async {
    try {
      // Check and request storage permission
      final storagePermission = await Permission.storage.request();
      if (storagePermission.isDenied || storagePermission.isPermanentlyDenied) {
        throw Exception(
            'Storage permission is required to access documents. Please grant permission in app settings.');
      }

      // Select multiple documents from device
      final FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: allowedExtensions ?? supportedExtensions,
        dialogTitle: dialogTitle ?? Labels.selectDocuments,
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        List<File> files = [];
        for (var file in result.files) {
          if (file.path != null) {
            files.add(File(file.path!));
          }
        }
        return files;
      }
      return [];
    } catch (e) {
      throw Exception('Failed to select documents: ${e.toString()}');
    }
  }

  /// Gets the file name from a file path
  static String getFileName(String filePath) {
    return filePath.split('/').last;
  }

  /// Gets the file extension from a file path
  static String getFileExtension(String filePath) {
    return filePath.split('.').last.toLowerCase();
  }

  /// Gets the file size in a readable format
  static String getFileSize(File file) {
    final bytes = file.lengthSync();
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Checks if file extension is supported
  static bool isSupportedFile(String filePath) {
    final extension = getFileExtension(filePath);
    return supportedExtensions.contains(extension);
  }

  /// Shows error message using SnackBar
  static void showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Shows success message using SnackBar
  static void showSuccessMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

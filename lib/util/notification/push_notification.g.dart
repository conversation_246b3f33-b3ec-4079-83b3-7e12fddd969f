// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'push_notification.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pushNotificationServiceHash() =>
    r'f44701f64b463c05281f17d22dc0399f3094e4f9';

/// See also [PushNotificationService].
@ProviderFor(PushNotificationService)
final pushNotificationServiceProvider =
    NotifierProvider<PushNotificationService, bool>.internal(
  PushNotificationService.new,
  name: r'pushNotificationServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$pushNotificationServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PushNotificationService = Notifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member

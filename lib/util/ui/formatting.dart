import 'package:intl/intl.dart';

/// duration to hh:mm:ss
String formattedDuration(Duration duration) {
  String twoDigits(int n) => n.toString().padLeft(2, "0");
  String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
  String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
  bool hasHours = duration.inHours != 0;
  return "${(hasHours) ? ('${twoDigits(duration.inHours)}:') : ''}$twoDigitMinutes:$twoDigitSeconds";
}

/// formats to specified - dd.mm.yyyy format
String formatDate(DateTime date) {
  final formatter = DateFormat('dd.MM.yyyy');
  return formatter.format(date);
}

/// formats to specified - yyyy-mm-dd format
String formatDateYMD(DateTime date) {
  final formatter = DateFormat('yyyy-MM-dd');
  return formatter.format(date);
}

/// formats from specified - dd.mm.yyyy format
DateTime? parseDate(String dateString) {
  final formatter = DateFormat('dd.MM.yyyy');
  try {
    return formatter.tryParse(dateString);
  } on FormatException catch (e) {
    // Handle parsing error (e.g., invalid date format)
    throw Exception("Invalid date format: $dateString");
  }
}

/// formats from specified - yyyy-mm-dd format
DateTime? parseDateYMD(String? date) {
  if (date == null) return null;
  final formatter = DateFormat('yyyy-MM-dd');

  return formatter.tryParse(date);
}

/// 12th Mar 2024
String formatDateWithOrdinalSuffix(DateTime date) {
  final day = date.day;
  // final suffix = getOrdinalSuffix(day);
  final formatter = DateFormat('MMM yyyy'); // Customize format as needed
  return '$day${getOrdinalSuffix(day)} ${formatter.format(date)}';
}

String getOrdinalSuffix(int day) {
  final ones = day % 10;
  final tens = (day ~/ 10) % 10;
  if (tens != 1) {
    switch (ones) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  } else {
    return 'th';
  }
}

/// formats to standard credit card format
String cardFormatName(String fullName, {int maxLength = 30}) {
  // Split the name by spaces
  List<String> nameParts = fullName.split(' ');

  // Handle cases where the name contains first, middle, and last names
  String firstName =
      toBeginningOfSentenceCase(nameParts.isNotEmpty ? nameParts.first : '');
  String lastName =
      toBeginningOfSentenceCase(nameParts.length > 1 ? nameParts.last : '');
  String middleInitials = toBeginningOfSentenceCase(nameParts.length > 2
      ? nameParts
          .sublist(1, nameParts.length - 1)
          .map((name) => name[0])
          .join(' ')
      : '');

  // Format the name using initials if needed
  String formattedName = '$firstName $middleInitials $lastName'.trim();

  // If the formatted name exceeds maxLength, reduce to initials
  if (formattedName.length > maxLength) {
    formattedName = '${firstName[0]} $middleInitials $lastName'.trim();
  }

  // If the name is still too long, truncate the last name
  if (formattedName.length > maxLength && lastName.isNotEmpty) {
    int remainingLength = maxLength - (formattedName.length - lastName.length);
    formattedName =
        '${firstName[0]} $middleInitials ${lastName.substring(0, remainingLength)}';
  }

  return formattedName.trim();
}

String capitalizeWords(String input) {
  if (input.isEmpty) return input;

  return input
      .split(' ')
      .map((word) {
        final cap = toBeginningOfSentenceCase(word.trim()).trim();
        return cap.isEmpty ? null : cap;
      })
      .nonNulls
      .join(" ");
}

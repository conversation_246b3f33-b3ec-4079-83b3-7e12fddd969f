import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:ako_basma/styles/theme.dart';

/// Service class for handling image operations (camera and gallery)
/// Follows the app's coding standards for reusability and clean code
class ImageService {
  static final ImagePicker _picker = ImagePicker();

  /// Opens camera to capture a photo
  /// Returns the captured image file or null if operation was cancelled
  static Future<File?> captureFromCamera({
    int imageQuality = 85,
    double? maxWidth = 1080,
    double? maxHeight = 1080,
  }) async {
    try {
      // Check and request camera permission
      final cameraPermission = await Permission.camera.request();
      if (cameraPermission.isDenied || cameraPermission.isPermanentlyDenied) {
        throw Exception(
            'Camera permission is required to take photos. Please grant permission in app settings.');
      }

      // Capture photo from camera
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: imageQuality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (photo != null) {
        return File(photo.path);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to capture photo: ${e.toString()}');
    }
  }

  /// Opens gallery to select an image
  /// Returns the selected image file or null if operation was cancelled
  static Future<File?> selectFromGallery({
    int imageQuality = 85,
    double? maxWidth = 1080,
    double? maxHeight = 1080,
  }) async {
    try {
      // Check and request storage permission (photos for Android 13+, storage for older versions)
      final storagePermission = await Permission.photos.request();
      if (storagePermission.isDenied || storagePermission.isPermanentlyDenied) {
        // Fallback to storage permission for older Android versions
        final fallbackPermission = await Permission.storage.request();
        if (fallbackPermission.isDenied ||
            fallbackPermission.isPermanentlyDenied) {
          throw Exception(
              'Gallery permission is required to select photos. Please grant permission in app settings.');
        }
      }

      // Select image from gallery
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: imageQuality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to select image: ${e.toString()}');
    }
  }

  /// Shows a bottom sheet with camera and gallery options
  /// Returns the selected image file or null if operation was cancelled
  static Future<File?> showImagePickerBottomSheet(BuildContext context) async {
    return await showModalBottomSheet<File?>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const _ImagePickerBottomSheet(),
    );
  }

  /// Shows error message using SnackBar
  static void showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Shows success message using SnackBar
  static void showSuccessMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

/// Private widget for image picker bottom sheet
class _ImagePickerBottomSheet extends StatelessWidget {
  const _ImagePickerBottomSheet();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            decoration: BoxDecoration(
              color: theme.cardColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                // Camera option
                _buildOption(
                  context,
                  icon: Icons.camera_alt,
                  title: 'Camera',
                  subtitle: 'Take a photo',
                  onTap: () async {
                    Navigator.pop(context);
                    try {
                      final image = await ImageService.captureFromCamera();
                      if (context.mounted) {
                        Navigator.pop(context, image);
                      }
                    } catch (e) {
                      if (context.mounted) {
                        ImageService.showErrorMessage(context, e.toString());
                      }
                    }
                  },
                ),

                // Divider
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  height: 1,
                  color: theme.dividerColor,
                ),

                // Gallery option
                _buildOption(
                  context,
                  icon: Icons.photo_library,
                  title: 'Gallery',
                  subtitle: 'Choose from gallery',
                  onTap: () async {
                    Navigator.pop(context);
                    try {
                      final image = await ImageService.selectFromGallery();
                      if (context.mounted) {
                        Navigator.pop(context, image);
                      }
                    } catch (e) {
                      if (context.mounted) {
                        ImageService.showErrorMessage(context, e.toString());
                      }
                    }
                  },
                ),
              ],
            ),
          ),

          // Cancel button
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: double.infinity,
            child: TextButton(
              onPressed: () => Navigator.pop(context),
              style: TextButton.styleFrom(
                backgroundColor: theme.cardColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(
                'Cancel',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final textStyles = theme.extension<TextStyles>()!;
    final colors = theme.extension<AppColors>()!;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Icon container
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: theme.primaryColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: theme.primaryColor,
                size: 24,
              ),
            ),

            // Text content
            Expanded(
              child: Container(
                margin: const EdgeInsets.only(left: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: textStyles.headline4,
                    ),
                    Container(
                      margin: const EdgeInsets.only(top: 2),
                      child: Text(
                        subtitle,
                        style: textStyles.body,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:riverpod_annotation/riverpod_annotation.dart';

// import '../../util/location/location.dart';

// part 'focus.g.dart';

/// Uncomment if a central map focus provider and controller is needed.

// @riverpod
// class MapFocus extends _$MapFocus {
  
//   @override
//   LatLng? build() {
//     _initMapFocus();
//     return null;
//   }

//   void _initMapFocus() async {
//     final location = await getCurrentLocation();
//     state = location;
//   }

//   void focusOn(LatLng latLng) {
//     state = null;
//     state = latLng;
//   }

//   void focusOnCurrent() {
//     state = null;
//     _initMapFocus();
//   }
// }

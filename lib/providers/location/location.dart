// import 'dart:async';
// import 'package:flutter/foundation.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:riverpod_annotation/riverpod_annotation.dart';

// import '../../util/hive/hive_util.dart';
// import '../../util/location/location.dart';

// part 'location.g.dart';

// /// for looped location updates in a provider.
// /// uncomment if needed
// @riverpod
// class UserLocation extends _$UserLocation {
//   late Timer timer;
//   static const _updateInterval = Duration(seconds: 20);
//   // 5. override the [build] method to return a [FutureOr]
//   // @override
//   // FutureOr<String?> build() {
//   //   // Hive.box(HiveKeys.appPrefsBox).listenable(keys:[HiveKeys.token]).addListener(() {
//   //   //   state = Hive.box(HiveKeys.appPrefsBox).get(HiveKeys.token);

//   //   // });
//   //   return Hive.box(HiveKeys.appPrefsBox).get(HiveKeys.token);
//   //   // 6. return a value (or do nothing if the return type is void)
//   // }
//   @override
//   LatLng? build() {
//     _init();
//     return HiveUtils.lastLocation();
//   }

//   void _init() async {
//     state = await getCurrentLocation();
//   }

//   void startUpdates() {
//     timer = Timer.periodic(_updateInterval, (_) async {
//       if (kDebugMode) {
//         print('userlocation update called');
//       }
//       final loc = await getCurrentLocation();
//       if (loc != null) {
//         state = loc;
//         HiveUtils.setLastLocation(loc);
//       }
//     });
//   }

//   void refresh() async {
//     final loc = await getCurrentLocation();
//     if (loc != null) {
//       state = loc;
//       HiveUtils.setLastLocation(loc);
//     }
//   }

//   void cancelUpdates() {
//     timer.cancel();
//   }
// }

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:ako_basma/util/hive/hive_util.dart';

class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  static const String _themeModeKey = 'theme_mode';

  ThemeModeNotifier() : super(ThemeMode.system) {
    _loadThemeMode();
  }

  /// Load saved theme mode from Hive storage
  Future<void> _loadThemeMode() async {
    try {
      final box = await Hive.openBox(HiveUtils.accBoxKey);
      final savedThemeMode = box.get(_themeModeKey);

      if (savedThemeMode != null) {
        // Convert saved string back to ThemeMode enum
        switch (savedThemeMode) {
          case 'light':
            state = ThemeMode.light;
            break;
          case 'dark':
            state = ThemeMode.dark;
            break;
          case 'system':
          default:
            state = ThemeMode.system;
            break;
        }
      } else {
        // Default to system theme if nothing is saved
        state = ThemeMode.system;
      }
    } catch (e) {
      // If there's an error loading, default to system theme
      state = ThemeMode.system;
    }
  }

  /// Save theme mode to Hive storage
  Future<void> _saveThemeMode(ThemeMode themeMode) async {
    try {
      final box = await Hive.openBox(HiveUtils.accBoxKey);
      String themeModeString;

      switch (themeMode) {
        case ThemeMode.light:
          themeModeString = 'light';
          break;
        case ThemeMode.dark:
          themeModeString = 'dark';
          break;
        case ThemeMode.system:
        default:
          themeModeString = 'system';
          break;
      }

      await box.put(_themeModeKey, themeModeString);
    } catch (e) {
      // Handle error if needed
      print('Error saving theme mode: $e');
    }
  }

  /// Toggle between light and dark theme modes
  /// When toggle is true = dark mode, false = light mode
  Future<void> toggleTheme(bool isDarkMode) async {
    final newThemeMode = isDarkMode ? ThemeMode.dark : ThemeMode.light;
    state = newThemeMode;
    await _saveThemeMode(newThemeMode);
  }

  /// Set specific theme mode
  Future<void> setThemeMode(ThemeMode themeMode) async {
    state = themeMode;
    await _saveThemeMode(themeMode);
  }

  /// Check if current theme is dark mode
  bool get isDarkMode => state == ThemeMode.dark;

  /// Check if current theme is light mode
  bool get isLightMode => state == ThemeMode.light;

  /// Check if current theme follows system setting
  bool get isSystemMode => state == ThemeMode.system;
}

/// Provider for theme mode management
final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>(
  (ref) => ThemeModeNotifier(),
);

/// Convenient provider to check if current theme is dark mode
final isDarkModeProvider = Provider<bool>((ref) {
  final themeMode = ref.watch(themeModeProvider);
  return themeMode == ThemeMode.dark;
});

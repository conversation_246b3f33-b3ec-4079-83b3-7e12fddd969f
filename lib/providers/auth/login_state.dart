import 'package:ako_basma/providers/auth/auth.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

import '../../util/hive/hive_util.dart';

part 'login_state.g.dart';

enum LoginStates {
  loggedOut,
  notAllowed,
  allowed,
  waiting,
  expired,
  error,
  offline,
}

@riverpod
class LoginState extends _$LoginState {
  @override
  LoginStates build() {
    final token = HiveUtils.token();
    if (kDebugMode) {
      print('found token = $token');
    }
    if (token == null) {
      return LoginStates.loggedOut;
    } else {
      refreshLoginState();
      return LoginStates.waiting;
    }
  }

  /// call this when you are just
  /// logging in,, signing in.,,, or the app is started...
  /// AFTER setting token..
  /// if token isnt there - it goes to logged out
  /// if tokens there.. it gets server profile.. and checks for isVerified..
  /// and selects the apppropriate state. and sets it.
  Future<void> refreshLoginState({LoginStates? overrideState}) async {
    if (overrideState != null) {
      state = overrideState;
      return;
    }
    final token = HiveUtils.token();
    if (token == null) {
      state = LoginStates.loggedOut;
      return;
    }

    state = LoginStates.waiting;
    bool allowed = false;
    try {
      final ic = InternetConnection.createInstance(
          useDefaultOptions: false,
          customCheckOptions: [
            //for CN
            'https://www.baidu.com',
            'https://wechat.com',

            // website
            //TODO: Add a server url here

            //default list
            'https://google.com',
            'https://one.one.one.one',
            'https://icanhazip.com/',
            'https://jsonplaceholder.typicode.com/todos/1',
            'https://reqres.in/api/users/1',
          ].map((url) => InternetCheckOption(uri: Uri.parse(url))).toList());

      bool hasInternetAccess = await ic.hasInternetAccess;
      if (!hasInternetAccess) {
        state = LoginStates.offline;
        return;
      }

      final profile =
          await ref.read(authStateProvider.notifier).refreshDeviceProfile();

      /// We may add any login restrictions here
      allowed = true;

      // profile.isVerified ?? false;
    } catch (e) {
      if (kDebugMode) {
        print('error in token checker..');
        print(e);
      }
      if (e is DioException) {
        final code = (e).response?.statusCode;
        if (code == 404 || code == 401) {
          if (kDebugMode) {
            print('setting expired screen....');
          }
          state = LoginStates.expired;
          return;
        }
      }
      if (kDebugMode) {
        print('setting error screen....');
      }

      state = LoginStates.error;
      return;
    }

    state = allowed ? LoginStates.allowed : LoginStates.notAllowed;
  }
}

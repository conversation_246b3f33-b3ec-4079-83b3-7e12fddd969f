import 'dart:io';
import 'dart:ui';
import 'package:ako_basma/components/form/location_selector.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:dio/dio.dart';
import 'package:intl/intl.dart';

import '../../model/profile.dart';
import '../../util/ui/formatting.dart';
import 'keys.dart';

typedef ApiClientException = DioException;
typedef ApiClientResponse<T> = Response<T>;
typedef ApiClientRequestOptions = RequestOptions;
typedef _ResponseData = Map<String, dynamic>;

extension ApiClientExceptionX on ApiClientException {
  String get responseMessage {
    const defaultError = "Some error occured";
    final msg = response?.data?['message'];
    if (msg == null) {
      return defaultError;
    }
    if (msg is Map) {
      return msg.values.firstOrNull ?? defaultError;
    }
    if (msg is String) {
      return msg;
    }
    // return !(response?.data?['message'] is String)?   response?.data?['message'] as String?;
    if (kDebugMode) {
      print("Response error was unmatched. Check the network logs.");
    }
    return defaultError;
  }
}

extension APIErrorObject on Object {
  String get errorMsg => (this is ApiClientException)
      ? ((this as ApiClientException).responseMessage)
      : toString();
}

/// An API client that makes network requests.
///
/// This class is meant to be seen as a representation of the common API contract
/// or API list (such as Swagger or Postman) given by the backend.
///
/// This class does not maintain authentication state, but rather receive the token
/// from external source.
///
/// When a widget or provider wants to make a network request, it should not
/// instantiate this class, but instead call the provider (api service) that exposes an object
/// of this type.

class ApiClient {
  static final BaseOptions _defaultOptions = BaseOptions(
    baseUrl: baseUrl,
    headers: {
      'Content-Type': 'application/json',
      'apiKey': authorization,
      'Accept': 'application/json',
    },
    listFormat: ListFormat.csv,
  );

  final Dio _httpClient;

  /// Creates an [ApiClient] with default options.
  ApiClient() : _httpClient = Dio(_defaultOptions);

  /// Creates an [ApiClient] with [token] set for authorization.
  ApiClient.withToken(String token)
      : _httpClient =
            Dio(_defaultOptions.copyWith(listFormat: ListFormat.csv, headers: {
          'Content-Type': 'application/json',
          'apiKey': authorization,
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        }));

  @override
  String toString() {
    return "ApiClient(_httpClient.options.headers['Authorization']: ${_httpClient.options.headers['Authorization']})";
  }

  Future<void> sendVerificationCode({String? phone, String? email}) async {
    final response = await _httpClient.post(
      'sendLoginOtp',
      data: {
        'type': email != null ? 'email' : 'phone',
        'identifier': email ?? phone,
      },
    );
    // await Future.delayed(const Duration(milliseconds: 1200));
  }

  /// AUTH / PROFILE FUNCTIONS

  Future<void> verifyVerificationCode({
    required String code,
    String? phone,
    String? email,
  }) async {
    await _httpClient.post(
      'verifyOtp',
      data: {
        'type': email != null ? 'email' : 'phone',
        'otp': code,
        'identifier': email ?? phone,
      },
    );
    await Future.delayed(const Duration(milliseconds: 1200));
  }

  Future<void> sendResetEmail({String? email, String? phone}) async {
    final response = await _httpClient.post(
      'user/forgetPassword',
      data: {
        if (email != null) 'email': email,
        if (phone != null) 'phone': phone,
      },
    );
    print(response.data);
  }

  Future<void> resetEmailPassword({
    required String token,
    String? email,
    String? phone,
    required String password,
  }) async {
    final response = await _httpClient.post(
      'user/resetPassword',
      data: {
        "token": token,
        if (email != null) "email": email,
        if (phone != null) "phone": phone,
        "password": password,
      },
    );
  }

  Future<(Profile, String)> signUp({
    required String name,
    required String email,
    required String password,
    String? phone,
    String? referCode,
    String? bday,
  }) async {
    final response = await _httpClient.post(
      'user/register',
      data: {
        "name": name,
        "email": email,
        "password": password,
        if (phone != null && phone.trim().isNotEmpty) "phone": phone,
        if (referCode != null && referCode.trim().isNotEmpty)
          "refferedBy": referCode,
        if (bday != null) 'dob': bday,
      },
    );
    final profile = Profile.fromMap(response.data['user'] as _ResponseData);
    final token = response.data['token'] as String;
    return (profile, token);
  }

  Future<(Profile, String)> loginWithEmail(
      String email, String password) async {
    final response = await _httpClient.post(
      'user/login',
      data: {
        "email": email,
        "password": password,
      },
    );
    final profile = Profile.fromMap(response.data['user'] as _ResponseData);
    final token = response.data['token'] as String;
    return (profile, token);
  }

  Future<(Profile, String)> loginWithPhone(
      String phone, String password) async {
    final response = await _httpClient.post(
      'user/login',
      data: {
        "phone": phone,
        "password": password,
      },
    );
    final profile = Profile.fromMap(response.data['user'] as _ResponseData);
    final token = response.data['token'] as String;
    return (profile, token);
  }

  Future<(Profile, String)> loginWithResetToken({
    String? email,
    String? phone,
    required String verificationToken,
    required String password,
  }) async {
    if (phone != null && email != null || phone == null && email == null) {
      throw 'You can\'t specify both email and phone in this function';
    }
    final response = await _httpClient.post(
      'user/resetPassword',
      data: {
        if (email != null) "email": email,
        if (phone != null) "phone": phone,
        'token': verificationToken,
        "password": password,
        "existing_member": "YES",
      },
    );
    final profile = Profile.fromMap(response.data['user'] as _ResponseData);
    final token = response.data['token'] as String;
    return (profile, token);
  }

  Future<Profile> profile() async {
    final response = await _httpClient.get(
      'user/profile',
    );
    return (Profile.fromMap(response.data['data'] as _ResponseData));
  }

  Future<void> logOut({String? token}) async {
    await _httpClient.post(
      'user/logout',
      options: token != null
          ? Options(headers: {'Authorization': 'Bearer $token'})
          : null,
    );
  }

  Future<void> updateProfile({
    String? name,
    String? phone,
    String? address,
    String? zipCode,
    DateTime? dateOfBirth,
    String? gender,
    String? fcmToken,
  }) async {
    final data = {
      if (name != null) 'name': name,
      if (phone != null) 'phone': phone,
      if (address != null) 'address': address,
      if (zipCode != null) 'postal_code': zipCode,
      if (gender != null) 'gender': gender,
      if (dateOfBirth != null)
        'dob': dateOfBirth == null ? null : formatDateYMD(dateOfBirth),
      if (fcmToken != null) 'fcm_token': fcmToken,
    };

    await _httpClient.put(
      'user/profile',
      data: data,
    );
  }

  /// type: 2: profilepic..
  Future<void> updateImage(
    String path, {
    required int type,
    String? tag,
  }) async {
    var headers = <String, String>{
      'apiKey': _httpClient.options.headers['apiKey'],
      if (_httpClient.options.headers['Authorization'] != null)
        'Authorization': _httpClient.options.headers['Authorization'],
    };
    var request = http.MultipartRequest('POST',
        Uri.parse('${_defaultOptions.baseUrl}storage/upload?type=$type'));
    final file = await http.MultipartFile.fromPath('file', path);
    request.files.add(file);
    request.headers.addAll(headers);
    http.StreamedResponse response = await request.send();

    if (response.statusCode == 200) {
      if (kDebugMode) {
        print(await response.stream.bytesToString());
      }
    } else {
      if (kDebugMode) {
        print(response.reasonPhrase);
      }
    }
  }

  // AUTH / PROFILE FUNCTIONS ENDS

  /// standard paginated data fetching.
  Future<(List<dynamic>, bool isLastPage)> fetchList({
    int page = 1,
    int limit = 10,
    CancelToken? cancelToken,
  }) async {
    // sorting filters.
    // final sortData =
    //     filters?.sortBy == null ? <String>[] : filters!.sortBy!.split('-');
    // final sortField = sortData.length == 2 ? sortData[0] : null;
    // final sortType = sortData.length == 2 ? sortData[1] : null;

    final response = await _httpClient.get(
      'public/listURL',
      queryParameters: {
        'page': page,
        'limit': limit,
      },
      cancelToken: cancelToken,
      options: Options(
        /// use this for multiple param values on a key (like a list)
        // listFormat: ListFormat.csv,

        validateStatus: (status) {
          if (status == 200) return true;
          if (status == 404) return true;
          return false;
        },
      ),
    );
    if (response.statusCode == 404) {
      return (<dynamic>[], true);
    }
    final isLastPage =
        !((response.data['data'] as _ResponseData)['hasNextPage'] ?? false);
    final list = ((response.data['data'] as _ResponseData)['list'] as List)
        .map((e) => "e.fromJson()")
        .toList();
    return (list, isLastPage);
  }

  /// standard non-paginated list fetching.
  Future<List<dynamic>> fetchFixedList({String? type}) async {
    final response = await _httpClient.get(
      'user/fixedList',
      queryParameters: {
        'type': type,
      },
      options: Options(
        validateStatus: (status) {
          if (status == 200) return true;
          if (status == 404) return true;
          return false;
        },
      ),
    );
    if (response.statusCode == 404) {
      return (<dynamic>[]);
    }
    final list = (((response.data as _ResponseData)['data'] ?? []) as List)
        .map((e) => "Class.fromMap(e)")
        .toList();
    return list;
  }

  ///types\
  ///data_deletion_request, account_deletion_request, business_inquiry, feedback, other
  Future<void> createRequest(
      {String? email,
      String? phone,
      String? name,
      String? type,
      String? subject,
      String? content}) async {
    final requestType = [
      'data_deletion_request',
      'business_inquiry',
      'feedback',
      'account_deletion_request'
    ].contains(type)
        ? type
        : 'other';
    await _httpClient.post(
      'public/memberRequest',
      data: {
        "email": email,
        "phone": phone,
        "name": name,
        "type":
            requestType, //data_deletion_request, business_inquiry, feedback, other
        "subject": subject,
        "content": content,
      },
    );
  }

  // GOOGLE location functions..
  // they are also there in utils. use either.

  Future<List<LocationId>> searchLocation(String query) async {
    final response = await _httpClient.postUri(
      Uri.parse('https://places.googleapis.com/v1/places:searchText'),
      data: {
        "textQuery": query,
      },
      options: Options(
        preserveHeaderCase: true,
        headers: {
          'Content-Type': 'application/json',
          'X-Goog-Api-Key': mapsKey,
          'X-Goog-FieldMask':
              'places.displayName,places.formattedAddress,places.location',
          'apiKey': null,
          'authorization': null,
        },
      ),
    );
    {
// sample response..
//   "places": [
//     {
//       "formattedAddress": "Delhi, India",
//       "location": {
//         "latitude": 28.7040592,
//         "longitude": 77.102490199999991
//       },
//       "displayName": {
//         "text": "Delhi",
//         "languageCode": "en"
//       }
//     }
//   ]
// }

      if (response.data.isEmpty) {
        return [];
      }

      final places = response.data['places'] as List;
      final list = places
          .map(
            (e) => LocationId(
              label: e['displayName']['text'],
              address: e['formattedAddress'],
              latlng: LatLng(
                e['location']['latitude'],
                e['location']['longitude'],
              ),
            ),
          )
          .toList();

      return list;
    }
  }

  Future<LocationId?> getLocation(LatLng latLng, [Locale? locale]) async {
    final response = await _httpClient.getUri(
      Uri.parse(
        'https://maps.google.com/maps/api/geocode/json?latlng=${'${latLng.latitude},${latLng.longitude}'}&key=$mapsKey',
      ),
      options: Options(
        preserveHeaderCase: true,
        headers: {
          'Content-Type': 'application/json',
          // 'X-Goog-Api-Key': mapsKey,
          // 'X-Goog-FieldMask':
          //     'places.displayName,places.formattedAddress,places.location',
          'apiKey': null,
          'authorization': null,
        },
      ),
    );

    if (response.data.isEmpty) {
      return null;
    }

    final obj = (response.data['results'] as List).firstOrNull;
    if (obj == null) return null;
    final address = obj['formatted_address'];
    if (address == null) return null;
    final data = LocationId(
      label: address,
      address: address,
      latlng: latLng,
    );

    return data;
  }
}

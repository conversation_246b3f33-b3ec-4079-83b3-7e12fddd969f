name: ako_basma
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ">=3.4.4 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  firebase_core: ^3.11.0
  firebase_messaging: ^15.2.2
  flutter_libphonenumber: ^2.5.0
  flutter_native_splash: ^2.4.1
  flutter_riverpod: ^2.6.1
  hive_flutter: ^1.1.0
  device_info_plus: ^11.2.2
  package_info_plus: ^8.2.0
  flutter_animate: ^4.5.2
  jumping_dot: ^0.0.7
  url_launcher: ^6.3.1
  flutter_markdown: ^0.7.6+1
  google_fonts: ^6.2.1
  email_validator: ^3.0.0
  google_maps_flutter: ^2.10.0
  dio: ^5.8.0+1
  geolocator: ^13.0.2
  flutter_local_notifications: ^18.0.1
  riverpod_annotation: ^2.6.1
  go_router: ^14.7.2
  internet_connection_checker_plus: ^2.7.0
  intl: ^0.19.0
  another_flushbar: ^1.12.30
  fl_country_code_picker: ^0.0.4
  auto_size_text: ^3.0.0
  remixicon: ^1.3.0
  font_awesome_flutter: ^10.8.0
  carousel_slider: ^5.0.0
  smooth_page_indicator: ^1.2.0+3
  cached_network_image: ^3.4.1
  permission_handler: ^11.3.1
  iconly: ^1.0.1
  animated_bottom_navigation_bar: ^1.3.3
  lottie: ^3.1.3
  pinput: ^5.0.1
  image_picker: ^1.1.2
  time_picker_with_timezone: ^0.0.7
  top_snackbar_flutter: ^2.0.0
  flutter_svg: ^2.0.17
  file_picker: ^10.1.2
  dotted_border: ^2.1.0
  signature: ^5.4.1
  uuid: ^4.5.1
  provider: ^6.1.5
  solar_icons: ^0.0.5
  dots_indicator: ^4.0.1
  flutter_chat_ui: ^1.6.15
  fluttertoast: ^8.2.12
  flutter_gemini: ^3.0.0
  day_night_switch: ^1.0.4
  iconsax_flutter: ^1.0.1
  shared_preferences: ^2.3.3
  flutter_sound: ^9.2.13
  audioplayers: ^5.2.1
  path_provider: ^2.1.4

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0
  riverpod_generator: ^2.6.2
  build_runner: ^2.4.13

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/ako_basma_logo.png
    - assets/icons/workspace_screen/Vector.svg
    - assets/icons/workspace_screen/schedule.svg
    - assets/icons/workspace_screen/request_leave.svg
    - assets/icons/workspace_screen/money.svg
    - assets/images/company_news.png
    - assets/icons/home_screen/arrow_end.svg
    - assets/icons/home_screen/clock.svg
    - assets/icons/home_screen/edit_pen.svg
    - assets/icons/home_screen/draw_sign.svg
    - assets/icons/home_screen/location.svg
    - assets/icons/home_screen/add_note.svg
    - assets/icons/home_screen/notification.svg
    - assets/icons/home_screen/arrow_end.svg
    - assets/icons/home_screen/clock.svg
    - assets/images/person.png
    - assets/icons/home_screen/notification-bing.svg
    - assets/images/news.png
    - assets/icons/chat.svg
    - assets/icons/home.svg
    - assets/icons/profile.svg
    - assets/icons/tasks.svg
    - assets/icons/workspace_screen/task.svg
    - assets/images/location_dark.png
    - assets/images/stack.svg
    - assets/images/server.svg
    - assets/images/employee.png
    - assets/icons/salary_screen/document-payslip.svg
    - assets/icons/salary_screen/download.svg
    - assets/icons/workspace_screen/upload.svg
    - assets/images/create_task.png
    - assets/images/location_light.png
    - assets/icons/workspace_screen/message.svg
    - assets/icons/workspace_screen/file.svg
    - assets/icons/workspace_screen/calendar_minimal.svg
    - assets/icons/workspace_screen/clock_circle.svg
    - assets/icons/workspace_screen/map_point.svg
    - assets/icons/tasks_screen/add.svg
    - assets/icons/profile_screen/chat_with_hr.svg
    - assets/icons/profile_screen/logout.svg
    - assets/icons/profile-circle.svg
    - assets/icons/tasks_screen/searchbar.svg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  fonts:
    - family: IBM Plex Sans Arabic
      fonts:
        - asset: assets/fonts/IBMPlexSansArabic-Bold.ttf
          weight: 700
        - asset: assets/fonts/IBMPlexSansArabic-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/IBMPlexSansArabic-Medium.ttf
          weight: 500
        - asset: assets/fonts/IBMPlexSansArabic-Regular.ttf
          weight: 400
        - asset: assets/fonts/IBMPlexSansArabic-Light.ttf
          weight: 300
        - asset: assets/fonts/IBMPlexSansArabic-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/IBMPlexSansArabic-Thin.ttf
          weight: 100
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
